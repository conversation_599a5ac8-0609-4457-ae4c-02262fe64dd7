// 全局变量
let currentProject = null;
let currentTaskId = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    showProjects();
});

// 显示项目列表页面
function showProjects() {
    hideAllPages();
    document.getElementById('projects-page').style.display = 'block';
    loadProjects();
}

// 隐藏所有页面
function hideAllPages() {
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.style.display = 'none');
}

// 加载项目列表
async function loadProjects() {
    try {
        const response = await fetch('/api/projects');
        const projects = await response.json();
        
        const projectsList = document.getElementById('projects-list');
        projectsList.innerHTML = '';
        
        projects.forEach(project => {
            const projectCard = createProjectCard(project);
            projectsList.appendChild(projectCard);
        });
    } catch (error) {
        console.error('加载项目列表失败:', error);
        showAlert('加载项目列表失败', 'danger');
    }
}

// 创建项目卡片
function createProjectCard(project) {
    const col = document.createElement('div');
    col.className = 'col-md-6 col-lg-4 mb-3';
    
    col.innerHTML = `
        <div class="card task-card h-100">
            <div class="card-body">
                <h5 class="card-title">${project.name}</h5>
                <p class="card-text">${project.description || '暂无描述'}</p>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="bi bi-folder"></i> ${project.work_dir}
                    </small>
                </div>
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="bi bi-calendar"></i> ${formatDate(project.created_at)}
                    </small>
                </div>
            </div>
            <div class="card-footer">
                <button class="btn btn-primary btn-sm" onclick="showProjectDetail('${project.project_id}')">
                    <i class="bi bi-eye"></i> 查看详情
                </button>
            </div>
        </div>
    `;
    
    return col;
}

// 显示创建项目页面
function showCreateProject() {
    hideAllPages();
    document.getElementById('create-project-page').style.display = 'block';
    
    // 清空表单
    document.getElementById('create-project-form').reset();
}

// 处理创建项目表单提交
document.getElementById('create-project-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('project-name').value,
        work_dir: document.getElementById('project-dir').value,
        description: document.getElementById('project-desc').value,
        requirement: document.getElementById('project-requirement').value
    };
    
    try {
        const response = await fetch('/api/projects', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('项目创建成功', 'success');
            showProjects();
        } else {
            showAlert(result.message || '创建项目失败', 'danger');
        }
    } catch (error) {
        console.error('创建项目失败:', error);
        showAlert('创建项目失败', 'danger');
    }
});

// 显示项目详情页面
async function showProjectDetail(projectId) {
    currentProject = projectId;
    hideAllPages();
    document.getElementById('project-detail-page').style.display = 'block';
    
    await loadProjectDetail(projectId);
    await loadProjectTasks(projectId);
}

// 加载项目详情
async function loadProjectDetail(projectId) {
    try {
        const response = await fetch(`/api/projects/${projectId}`);
        const project = await response.json();
        
        document.getElementById('project-title').innerHTML = 
            `<i class="bi bi-folder-open"></i> ${project.name}`;
        document.getElementById('project-description').textContent = 
            project.description || '暂无描述';
        document.getElementById('project-work-dir').textContent = project.work_dir;
        document.getElementById('project-created-at').textContent = 
            formatDate(project.created_at);
            
        // 加载项目统计
        const summaryResponse = await fetch(`/api/projects/${projectId}/summary`);
        const summary = await summaryResponse.json();
        
        document.getElementById('total-tasks').textContent = summary.total_tasks || 0;
        document.getElementById('completed-tasks').textContent = summary.completed_tasks || 0;
        document.getElementById('pending-tasks').textContent = summary.pending_tasks || 0;
        document.getElementById('failed-tasks').textContent = summary.failed_tasks || 0;
        
    } catch (error) {
        console.error('加载项目详情失败:', error);
        showAlert('加载项目详情失败', 'danger');
    }
}

// 加载项目任务列表
async function loadProjectTasks(projectId) {
    try {
        const response = await fetch(`/api/projects/${projectId}/tasks`);
        const tasks = await response.json();
        
        const tasksList = document.getElementById('tasks-list');
        tasksList.innerHTML = '';
        
        if (tasks.length === 0) {
            tasksList.innerHTML = '<p class="text-muted">暂无任务</p>';
            return;
        }
        
        tasks.forEach(task => {
            const taskCard = createTaskCard(task);
            tasksList.appendChild(taskCard);
        });
    } catch (error) {
        console.error('加载任务列表失败:', error);
        showAlert('加载任务列表失败', 'danger');
    }
}

// 创建任务卡片
function createTaskCard(task) {
    const div = document.createElement('div');
    div.className = 'card mb-3';
    
    const statusClass = getStatusClass(task.status);
    const statusText = getStatusText(task.status);
    const priorityClass = getPriorityClass(task.priority);
    
    div.innerHTML = `
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="card-title">
                        ${task.title}
                        <span class="badge ${statusClass} status-badge ms-2">${statusText}</span>
                        <span class="badge ${priorityClass} status-badge ms-1">${task.priority}</span>
                    </h6>
                    <p class="card-text">${task.description}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-calendar-plus"></i> 创建: ${formatDate(task.created_at)}
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="bi bi-calendar-check"></i> 更新: ${formatDate(task.updated_at)}
                            </small>
                        </div>
                    </div>
                    ${task.result ? `<div class="mt-2"><small class="text-success">结果: ${task.result}</small></div>` : ''}
                </div>
                <div class="ms-3">
                    <div class="btn-group-vertical" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="editTask('${task.id}')" 
                                ${task.status !== 'pending' ? 'disabled' : ''}>
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="runTask('${task.id}')"
                                ${task.status === 'completed' ? 'disabled' : ''}>
                            <i class="bi bi-play"></i> 执行
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="viewLogs('${task.id}')">
                            <i class="bi bi-file-text"></i> 日志
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="viewLLMLogs('${task.id}')">
                            <i class="bi bi-chat-dots"></i> LLM日志
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return div;
}

// 获取状态样式类
function getStatusClass(status) {
    const statusClasses = {
        'pending': 'bg-warning',
        'in_progress': 'bg-info',
        'completed': 'bg-success',
        'failed': 'bg-danger'
    };
    return statusClasses[status] || 'bg-secondary';
}

// 获取状态文本
function getStatusText(status) {
    const statusTexts = {
        'pending': '待执行',
        'in_progress': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };
    return statusTexts[status] || status;
}

// 获取优先级样式类
function getPriorityClass(priority) {
    const priorityClasses = {
        'low': 'bg-light text-dark',
        'medium': 'bg-warning',
        'high': 'bg-danger'
    };
    return priorityClasses[priority] || 'bg-secondary';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 显示警告消息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.insertBefore(alertDiv, document.body.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// 显示创建任务模态框
function showCreateTask() {
    currentTaskId = null;
    document.getElementById('taskModalTitle').textContent = '新增任务';
    document.getElementById('task-form').reset();
    document.getElementById('task-id').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
    modal.show();
}

// 编辑任务
async function editTask(taskId) {
    currentTaskId = taskId;
    
    try {
        const response = await fetch(`/api/projects/${currentProject}/tasks`);
        const tasks = await response.json();
        const task = tasks.find(t => t.id === taskId);
        
        if (!task) {
            showAlert('任务不存在', 'danger');
            return;
        }
        
        document.getElementById('taskModalTitle').textContent = '编辑任务';
        document.getElementById('task-id').value = task.id;
        document.getElementById('task-title').value = task.title;
        document.getElementById('task-description').value = task.description;
        document.getElementById('task-details').value = task.details || '';
        document.getElementById('task-priority').value = task.priority;
        document.getElementById('task-test-strategy').value = task.testStrategy || '';
        
        const modal = new bootstrap.Modal(document.getElementById('taskModal'));
        modal.show();
        
    } catch (error) {
        console.error('加载任务详情失败:', error);
        showAlert('加载任务详情失败', 'danger');
    }
}

// 保存任务
async function saveTask() {
    const taskData = {
        title: document.getElementById('task-title').value,
        description: document.getElementById('task-description').value,
        details: document.getElementById('task-details').value,
        priority: document.getElementById('task-priority').value,
        testStrategy: document.getElementById('task-test-strategy').value
    };

    try {
        let response;
        if (currentTaskId) {
            // 更新任务
            response = await fetch(`/api/projects/${currentProject}/tasks/${currentTaskId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });
        } else {
            // 创建任务
            response = await fetch(`/api/projects/${currentProject}/tasks`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(taskData)
            });
        }

        const result = await response.json();

        if (result.success || !result.error) {
            showAlert(currentTaskId ? '任务更新成功' : '任务创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('taskModal')).hide();
            await loadProjectTasks(currentProject);
            await loadProjectDetail(currentProject);
        } else {
            showAlert(result.error || result.message || '操作失败', 'danger');
        }
    } catch (error) {
        console.error('保存任务失败:', error);
        showAlert('保存任务失败', 'danger');
    }
}

// 执行任务
async function runTask(taskId) {
    try {
        const response = await fetch(`/api/projects/${currentProject}/tasks/${taskId}/run`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('任务开始执行', 'success');
            // 定期刷新任务状态
            const interval = setInterval(async () => {
                await loadProjectTasks(currentProject);
                await loadProjectDetail(currentProject);

                // 检查任务是否完成
                const tasksResponse = await fetch(`/api/projects/${currentProject}/tasks`);
                const tasks = await tasksResponse.json();
                const task = tasks.find(t => t.id === taskId);

                if (task && (task.status === 'completed' || task.status === 'failed')) {
                    clearInterval(interval);
                    showAlert(`任务执行${task.status === 'completed' ? '成功' : '失败'}`,
                             task.status === 'completed' ? 'success' : 'danger');
                }
            }, 2000);
        } else {
            showAlert(result.message || '任务执行失败', 'danger');
        }
    } catch (error) {
        console.error('执行任务失败:', error);
        showAlert('执行任务失败', 'danger');
    }
}

// 查看任务日志
async function viewLogs(taskId) {
    try {
        const response = await fetch(`/api/projects/${currentProject}/tasks/${taskId}/logs`);
        const result = await response.json();

        if (result.logs) {
            const logsHtml = result.logs.map(log =>
                `<div class="log-entry">${escapeHtml(log)}</div>`
            ).join('');

            showModal('任务日志', `<div class="log-viewer">${logsHtml}</div>`);
        } else {
            showAlert('获取日志失败', 'danger');
        }
    } catch (error) {
        console.error('获取日志失败:', error);
        showAlert('获取日志失败', 'danger');
    }
}

// 查看LLM交互日志
async function viewLLMLogs(taskId) {
    currentTaskId = taskId;
    await refreshLLMLogs();

    const modal = new bootstrap.Modal(document.getElementById('llmLogModal'));
    modal.show();
}

// 刷新LLM日志
async function refreshLLMLogs() {
    if (!currentTaskId) return;

    try {
        const response = await fetch(`/api/projects/${currentProject}/tasks/${currentTaskId}/llm-logs`);
        const result = await response.json();

        if (result.success && result.logs) {
            const outputLogs = [];
            const requestLogs = [];

            result.logs.forEach(log => {
                const logHtml = `
                    <div class="mb-2 p-2 border-bottom">
                        <small class="text-muted">[${log.timestamp}] ${log.progress}</small>
                        <div class="log-entry">${escapeHtml(log.content)}</div>
                    </div>
                `;

                if (log.log_type === 'Request') {
                    requestLogs.push(logHtml);
                } else {
                    outputLogs.push(logHtml);
                }
            });

            document.getElementById('llm-log-output').innerHTML =
                outputLogs.length > 0 ? outputLogs.join('') : '<p class="text-muted">暂无输出日志</p>';
            document.getElementById('llm-log-requests').innerHTML =
                requestLogs.length > 0 ? requestLogs.join('') : '<p class="text-muted">暂无请求日志</p>';
        } else {
            document.getElementById('llm-log-output').innerHTML = '<p class="text-muted">获取日志失败</p>';
            document.getElementById('llm-log-requests').innerHTML = '<p class="text-muted">获取日志失败</p>';
        }
    } catch (error) {
        console.error('获取LLM日志失败:', error);
        document.getElementById('llm-log-output').innerHTML = '<p class="text-danger">获取日志失败</p>';
        document.getElementById('llm-log-requests').innerHTML = '<p class="text-danger">获取日志失败</p>';
    }
}

// 重置项目任务
async function resetProjectTasks() {
    if (!confirm('确定要重置所有任务吗？这将清除所有任务的执行结果。')) {
        return;
    }

    try {
        const response = await fetch(`/api/projects/${currentProject}/tasks/reset`, {
            method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
            showAlert('任务重置成功', 'success');
            await loadProjectTasks(currentProject);
            await loadProjectDetail(currentProject);
        } else {
            showAlert(result.message || '任务重置失败', 'danger');
        }
    } catch (error) {
        console.error('重置任务失败:', error);
        showAlert('重置任务失败', 'danger');
    }
}

// 显示通用模态框
function showModal(title, content) {
    // 创建临时模态框
    const modalHtml = `
        <div class="modal fade" id="tempModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除之前的临时模态框
    const existingModal = document.getElementById('tempModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('tempModal'));
    modal.show();

    // 模态框关闭后移除
    document.getElementById('tempModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
