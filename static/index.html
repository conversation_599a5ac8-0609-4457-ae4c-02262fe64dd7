<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI任务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .task-card {
            transition: transform 0.2s;
        }
        .task-card:hover {
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.8em;
        }
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        .log-entry.collapsed {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .log-viewer {
            max-height: 500px;
            overflow-y: auto;
        }
        .llm-log-left {
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
        }
        .llm-log-right {
            background-color: #fff;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#" onclick="showProjects()">
                <i class="bi bi-cpu"></i> AI任务管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="showProjects()">项目列表</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 项目列表页面 -->
        <div id="projects-page" class="page">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-folder"></i> 项目列表</h2>
                <button class="btn btn-primary" onclick="showCreateProject()">
                    <i class="bi bi-plus"></i> 新建项目
                </button>
            </div>
            <div id="projects-list" class="row">
                <!-- 项目卡片将在这里动态加载 -->
            </div>
        </div>

        <!-- 创建项目页面 -->
        <div id="create-project-page" class="page" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-plus-circle"></i> 新建项目</h2>
                <button class="btn btn-secondary" onclick="showProjects()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
            </div>
            <form id="create-project-form">
                <div class="mb-3">
                    <label for="project-name" class="form-label">项目名称</label>
                    <input type="text" class="form-control" id="project-name" required>
                </div>
                <div class="mb-3">
                    <label for="project-dir" class="form-label">工作目录</label>
                    <input type="text" class="form-control" id="project-dir" required>
                </div>
                <div class="mb-3">
                    <label for="project-desc" class="form-label">项目描述</label>
                    <textarea class="form-control" id="project-desc" rows="3"></textarea>
                </div>
                <div class="mb-3">
                    <label for="project-requirement" class="form-label">项目需求</label>
                    <textarea class="form-control" id="project-requirement" rows="5"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">创建项目</button>
            </form>
        </div>

        <!-- 项目详情页面 -->
        <div id="project-detail-page" class="page" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 id="project-title"><i class="bi bi-folder-open"></i> 项目详情</h2>
                <div>
                    <button class="btn btn-warning me-2" onclick="resetProjectTasks()">
                        <i class="bi bi-arrow-clockwise"></i> 重置任务
                    </button>
                    <button class="btn btn-secondary" onclick="showProjects()">
                        <i class="bi bi-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
            
            <!-- 项目信息 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title">项目信息</h5>
                    <p id="project-description" class="card-text"></p>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>工作目录：</strong> <span id="project-work-dir"></span>
                        </div>
                        <div class="col-md-6">
                            <strong>创建时间：</strong> <span id="project-created-at"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务统计 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary" id="total-tasks">0</h5>
                            <p class="card-text">总任务数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success" id="completed-tasks">0</h5>
                            <p class="card-text">已完成</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning" id="pending-tasks">0</h5>
                            <p class="card-text">待执行</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-danger" id="failed-tasks">0</h5>
                            <p class="card-text">失败</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-list-task"></i> 任务列表</h5>
                    <button class="btn btn-primary btn-sm" onclick="showCreateTask()">
                        <i class="bi bi-plus"></i> 新增任务
                    </button>
                </div>
                <div class="card-body">
                    <div id="tasks-list">
                        <!-- 任务列表将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建/编辑任务模态框 -->
        <div class="modal fade" id="taskModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="taskModalTitle">新增任务</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="task-form">
                            <input type="hidden" id="task-id">
                            <div class="mb-3">
                                <label for="task-title" class="form-label">任务标题</label>
                                <input type="text" class="form-control" id="task-title" required>
                            </div>
                            <div class="mb-3">
                                <label for="task-description" class="form-label">任务描述</label>
                                <textarea class="form-control" id="task-description" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="task-details" class="form-label">任务详情</label>
                                <textarea class="form-control" id="task-details" rows="4"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="task-priority" class="form-label">优先级</label>
                                <select class="form-select" id="task-priority">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="task-test-strategy" class="form-label">测试策略</label>
                                <textarea class="form-control" id="task-test-strategy" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- LLM日志查看模态框 -->
        <div class="modal fade" id="llmLogModal" tabindex="-1">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">LLM交互日志</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-8 llm-log-left">
                                <h6>LLM输出</h6>
                                <div id="llm-log-output" class="log-viewer">
                                    <!-- LLM输出日志 -->
                                </div>
                            </div>
                            <div class="col-md-4 llm-log-right">
                                <h6>用户请求</h6>
                                <div id="llm-log-requests" class="log-viewer">
                                    <!-- 用户请求日志 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="refreshLLMLogs()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
