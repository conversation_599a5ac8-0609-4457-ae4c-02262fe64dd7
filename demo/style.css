* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

.game-container {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    max-width: 600px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.score-container {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

.score-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-size: 1.1em;
    font-weight: bold;
}

.label {
    margin-right: 10px;
    color: #4ecdc4;
}

.value {
    color: #ff6b6b;
    font-size: 1.2em;
}

.game-area {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.main-game {
    flex: 1;
    text-align: center;
}

#gameCanvas {
    border: 3px solid #4ecdc4;
    border-radius: 10px;
    background: #000;
    box-shadow: inset 0 0 20px rgba(78, 205, 196, 0.3);
}

.side-panel {
    width: 150px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.next-piece {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-align: center;
}

.next-piece h3 {
    margin-bottom: 10px;
    color: #4ecdc4;
    font-size: 1.1em;
}

#nextCanvas {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    background: #000;
    margin: 0 auto;
    display: block;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.game-btn {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.game-btn:hover {
    background: linear-gradient(45deg, #44a08d, #4ecdc4);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.game-btn:active {
    transform: translateY(0);
}

.game-btn:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.instructions {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.instructions h3 {
    margin-bottom: 10px;
    color: #ff6b6b;
    font-size: 1.1em;
}

.instruction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.9em;
}

.instruction-item span:first-child {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 5px;
    font-weight: bold;
    color: #4ecdc4;
}

.instruction-item span:last-child {
    color: #ccc;
}

.game-status {
    text-align: center;
    margin-top: 20px;
}

#statusMessage {
    font-size: 1.2em;
    color: #4ecdc4;
    font-weight: bold;
}

/* 方块颜色定义 */
.tetromino-I { background-color: #00f0f0; }
.tetromino-O { background-color: #f0f000; }
.tetromino-T { background-color: #a000f0; }
.tetromino-S { background-color: #00f000; }
.tetromino-Z { background-color: #f00000; }
.tetromino-J { background-color: #0000f0; }
.tetromino-L { background-color: #f0a000; }

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        padding: 15px;
        margin: 10px;
    }

    .game-header h1 {
        font-size: 2em;
    }

    .game-area {
        flex-direction: column;
        align-items: center;
    }

    .side-panel {
        width: 100%;
        max-width: 300px;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .next-piece,
    .controls,
    .instructions {
        flex: 1;
        min-width: 120px;
        margin: 5px;
    }

    #gameCanvas {
        width: 250px;
        height: 500px;
    }
}

@media (max-width: 480px) {
    .game-container {
        padding: 10px;
    }

    .score-container {
        flex-direction: column;
        gap: 10px;
    }

    .score-item {
        text-align: center;
    }

    #gameCanvas {
        width: 200px;
        height: 400px;
    }

    .game-btn {
        padding: 10px 15px;
        font-size: 0.9em;
    }
}