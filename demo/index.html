<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块 - 完整版</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>俄罗斯方块</h1>
            <div class="score-container">
                <div class="score-item">
                    <span class="label">分数:</span>
                    <span id="score" class="value">0</span>
                </div>
                <div class="score-item">
                    <span class="label">等级:</span>
                    <span id="level" class="value">1</span>
                </div>
                <div class="score-item">
                    <span class="label">行数:</span>
                    <span id="lines" class="value">0</span>
                </div>
            </div>
        </div>

        <div class="game-area">
            <div class="main-game">
                <canvas id="gameCanvas" width="300" height="600"></canvas>
            </div>

            <div class="side-panel">
                <div class="next-piece">
                    <h3>下一个</h3>
                    <canvas id="nextCanvas" width="120" height="80"></canvas>
                </div>

                <div class="controls">
                    <h3>控制</h3>
                    <button id="startBtn" class="game-btn">开始游戏</button>
                    <button id="pauseBtn" class="game-btn">暂停</button>
                    <button id="resetBtn" class="game-btn">重置</button>
                    <button id="settingsBtn" class="game-btn">设置</button>
                    <button id="statsBtn" class="game-btn">统计</button>
                </div>

                <div class="instructions">
                    <h3>操作说明</h3>
                    <div class="instruction-item">
                        <span>← →</span>
                        <span>左右移动</span>
                    </div>
                    <div class="instruction-item">
                        <span>↓</span>
                        <span>加速下降</span>
                    </div>
                    <div class="instruction-item">
                        <span>↑</span>
                        <span>旋转方块</span>
                    </div>
                    <div class="instruction-item">
                        <span>空格</span>
                        <span>直接落下</span>
                    </div>
                    <div class="instruction-item">
                        <span>C</span>
                        <span>暂存方块</span>
                    </div>
                    <div class="instruction-item">
                        <span>P</span>
                        <span>暂停/继续</span>
                    </div>
                </div>

                <div class="features">
                    <h3>高级功能</h3>
                    <div class="feature-item">
                        <span>🎵</span>
                        <span>音效系统</span>
                    </div>
                    <div class="feature-item">
                        <span>👻</span>
                        <span>幽灵方块</span>
                    </div>
                    <div class="feature-item">
                        <span>📊</span>
                        <span>游戏统计</span>
                    </div>
                    <div class="feature-item">
                        <span>💾</span>
                        <span>本地存储</span>
                    </div>
                    <div class="feature-item">
                        <span>🎨</span>
                        <span>粒子效果</span>
                    </div>
                    <div class="feature-item">
                        <span>📱</span>
                        <span>触摸控制</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="game-status">
            <span id="statusMessage">按开始游戏开始</span>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>