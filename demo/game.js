// 俄罗斯方块 - 完整版本（包含高级功能）
class TetrisGame {
    constructor() {
        // Canvas设置
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');

        // 游戏板设置
        this.boardWidth = 10;
        this.boardHeight = 20;
        this.blockSize = 30;

        // 游戏状态
        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.heldPiece = null;
        this.canHold = true;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameOver = false;
        this.isPaused = false;
        this.isStarted = false;
        this.dropTime = 0;
        this.dropInterval = 1000;

        // 统计数据
        this.stats = {
            totalPieces: 0,
            totalLines: 0,
            totalScore: 0,
            gamesPlayed: 0,
            bestScore: 0,
            playTime: 0,
            startTime: null
        };

        // 游戏设置
        this.settings = {
            soundEnabled: true,
            ghostPieceEnabled: true,
            gridEnabled: true,
            vibrationEnabled: true,
            difficulty: 'normal',
            controls: 'keyboard'
        };

        // 动画效果
        this.animations = [];
        this.particles = [];

        // 音效系统
        this.sounds = {
            move: null,
            rotate: null,
            drop: null,
            clear: null,
            hold: null,
            gameOver: null
        };

        // 方块颜色定义
        this.colors = {
            'I': '#00f0f0',  // 青色
            'O': '#f0f000',  // 黄色
            'T': '#a000f0',  // 紫色
            'S': '#00f000',  // 绿色
            'Z': '#f00000',  // 红色
            'J': '#0000f0',  // 蓝色
            'L': '#f0a000'   // 橙色
        };

        // 7种标准俄罗斯方块形状定义
        this.pieces = {
            'I': [
                [1, 1, 1, 1]
            ],
            'O': [
                [1, 1],
                [1, 1]
            ],
            'T': [
                [0, 1, 0],
                [1, 1, 1]
            ],
            'S': [
                [0, 1, 1],
                [1, 1, 0]
            ],
            'Z': [
                [1, 1, 0],
                [0, 1, 1]
            ],
            'J': [
                [1, 0, 0],
                [1, 1, 1]
            ],
            'L': [
                [0, 0, 1],
                [1, 1, 1]
            ]
        };

        // 墙踢数据（Super Rotation System）
        this.wallKicks = {
            '0->1': [[0, 0], [-1, 0], [-1, 1], [0, -2], [-1, -2]],
            '1->0': [[0, 0], [1, 0], [1, -1], [0, 2], [1, 2]],
            '1->2': [[0, 0], [1, 0], [1, -1], [0, 2], [1, 2]],
            '2->1': [[0, 0], [-1, 0], [-1, 1], [0, -2], [-1, -2]],
            '2->3': [[0, 0], [1, 0], [1, 1], [0, -2], [1, -2]],
            '3->2': [[0, 0], [-1, 0], [-1, -1], [0, 2], [-1, 2]],
            '3->0': [[0, 0], [-1, 0], [-1, -1], [0, 2], [-1, 2]],
            '0->3': [[0, 0], [1, 0], [1, 1], [0, -2], [1, -2]]
        };

        this.pieceTypes = Object.keys(this.pieces);

        // 初始化游戏
        this.loadSettings();
        this.loadStats();
        this.initSounds();
        this.init();
    }

    init() {
        this.initBoard();
        this.setupEventListeners();
        this.setupTouchControls();
        this.spawnPiece();
        this.updateDisplay();
        this.gameLoop();
    }

    // 初始化游戏板
    initBoard() {
        for (let y = 0; y < this.boardHeight; y++) {
            this.board[y] = new Array(this.boardWidth).fill(0);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));

        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => this.start());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());

        // 设置按钮
        document.getElementById('settingsBtn')?.addEventListener('click', () => this.showSettings());
        document.getElementById('statsBtn')?.addEventListener('click', () => this.showStats());
    }

    // 设置触摸控制
    setupTouchControls() {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;

        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            touchStartX = touch.clientX;
            touchStartY = touch.clientY;
            touchStartTime = Date.now();
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            const touch = e.changedTouches[0];
            const deltaX = touch.clientX - touchStartX;
            const deltaY = touch.clientY - touchStartY;
            const deltaTime = Date.now() - touchStartTime;

            // 处理触摸手势
            if (Math.abs(deltaX) > Math.abs(deltaY)) {
                // 水平滑动
                if (deltaX > 30) {
                    this.movePiece(1, 0);
                } else if (deltaX < -30) {
                    this.movePiece(-1, 0);
                }
            } else {
                // 垂直滑动
                if (deltaY > 30) {
                    this.movePiece(0, 1);
                } else if (deltaY < -30 && deltaTime < 200) {
                    this.rotatePiece();
                }
            }

            // 快速点击 - 硬降落
            if (deltaTime < 200 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
                this.hardDrop();
            }
        });
    }

    // 初始化音效
    initSounds() {
        // 使用Web Audio API创建音效
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();

            this.sounds.move = this.createSound(audioContext, 200, 0.1);
            this.sounds.rotate = this.createSound(audioContext, 300, 0.1);
            this.sounds.drop = this.createSound(audioContext, 150, 0.2);
            this.sounds.clear = this.createSound(audioContext, 500, 0.3);
            this.sounds.hold = this.createSound(audioContext, 400, 0.1);
            this.sounds.gameOver = this.createSound(audioContext, 100, 0.5);
        } catch (e) {
            console.log('音效初始化失败:', e);
        }
    }

    // 创建简单音效
    createSound(audioContext, frequency, duration) {
        return () => {
            if (!this.settings.soundEnabled) return;

            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = frequency;
            oscillator.type = 'square';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + duration);
        };
    }

    // 播放音效
    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName]();
        }
    }

    // 游戏状态管理
    start() {
        if (!this.isStarted) {
            this.isStarted = true;
            this.stats.startTime = Date.now();
            this.stats.gamesPlayed++;
        }

        if (this.gameOver) {
            this.reset();
        } else {
            this.isPaused = false;
            document.getElementById('pauseBtn').textContent = '暂停';
            document.getElementById('statusMessage').textContent = '游戏进行中';
        }
    }

    togglePause() {
        if (!this.gameOver && this.isStarted) {
            this.isPaused = !this.isPaused;
            document.getElementById('pauseBtn').textContent = this.isPaused ? '继续' : '暂停';
            document.getElementById('statusMessage').textContent = this.isPaused ? '游戏暂停' : '游戏进行中';
        }
    }

    reset() {
        // 保存统计数据
        if (this.isStarted && !this.gameOver) {
            this.stats.playTime += Date.now() - this.stats.startTime;
        }

        if (this.score > this.stats.bestScore) {
            this.stats.bestScore = this.score;
        }

        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.heldPiece = null;
        this.canHold = true;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameOver = false;
        this.isPaused = false;
        this.isStarted = false;
        this.dropTime = 0;
        this.dropInterval = 1000;

        this.initBoard();
        this.spawnPiece();
        this.updateDisplay();

        document.getElementById('statusMessage').textContent = '按开始游戏开始';
        document.getElementById('pauseBtn').textContent = '暂停';
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('startBtn').disabled = false;

        this.saveStats();
    }

    // 生成新方块
    spawnPiece() {
        if (this.nextPiece === null) {
            this.nextPiece = this.getRandomPiece();
        }

        this.currentPiece = this.nextPiece;
        this.nextPiece = this.getRandomPiece();

        // 设置方块初始位置
        this.currentPiece.x = Math.floor(this.boardWidth / 2) - Math.floor(this.currentPiece.shape[0].length / 2);
        this.currentPiece.y = 0;
        this.currentPiece.rotation = 0;

        // 更新统计
        this.stats.totalPieces++;

        // 检查游戏是否结束
        if (this.isCollision(this.currentPiece, 0, 0)) {
            this.gameOver = true;
            this.stats.playTime += Date.now() - this.stats.startTime;
            this.saveStats();
            this.showGameOver();
        }

        // 重置hold权限
        this.canHold = true;

        this.drawNextPiece();
    }

    // 获取随机方块
    getRandomPiece() {
        const type = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
        const shape = this.pieces[type];

        return {
            shape: shape,
            type: type,
            x: 0,
            y: 0,
            rotation: 0
        };
    }

    // 碰撞检测算法
    isCollision(piece, dx, dy, rotation = piece.rotation) {
        const newX = piece.x + dx;
        const newY = piece.y + dy;

        // 获取旋转后的形状
        let testShape = this.getRotatedShape(piece.shape, rotation);

        for (let y = 0; y < testShape.length; y++) {
            for (let x = 0; x < testShape[y].length; x++) {
                if (testShape[y][x]) {
                    const boardX = newX + x;
                    const boardY = newY + y;

                    // 检查边界
                    if (boardX < 0 || boardX >= this.boardWidth ||
                        boardY >= this.boardHeight) {
                        return true;
                    }

                    // 检查与已锁定方块的碰撞
                    if (boardY >= 0 && this.board[boardY][boardX] !== 0) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 获取旋转后的形状
    getRotatedShape(shape, rotation) {
        let rotated = shape;

        for (let i = 0; i < rotation; i++) {
            rotated = this.rotateMatrix(rotated);
        }

        return rotated;
    }

    // 矩阵旋转算法
    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = [];

        for (let x = 0; x < cols; x++) {
            rotated[x] = [];
            for (let y = rows - 1; y >= 0; y--) {
                rotated[x][rows - 1 - y] = matrix[y][x];
            }
        }

        return rotated;
    }

    // 处理键盘输入
    handleKeyPress(e) {
        if (this.gameOver || !this.isStarted) return;

        switch(e.key) {
            case 'ArrowLeft':
                if (!this.isPaused) this.movePiece(-1, 0);
                break;
            case 'ArrowRight':
                if (!this.isPaused) this.movePiece(1, 0);
                break;
            case 'ArrowDown':
                if (!this.isPaused) this.movePiece(0, 1);
                break;
            case 'ArrowUp':
                if (!this.isPaused) this.rotatePiece();
                break;
            case ' ':
                if (!this.isPaused) this.hardDrop();
                break;
            case 'c':
            case 'C':
                if (!this.isPaused) this.holdPiece();
                break;
            case 'p':
            case 'P':
                this.togglePause();
                break;
        }
    }

    // 移动方块
    movePiece(dx, dy) {
        if (!this.isCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;

            // 如果是向下移动，增加分数
            if (dy > 0) {
                this.score += 1;
                this.updateDisplay();
                this.playSound('move');
            }

            this.draw();
        } else if (dy > 0) {
            // 向下移动时发生碰撞，锁定方块
            this.lockPiece();
        }
    }

    // 旋转方块（使用Super Rotation System）
    rotatePiece() {
        const newRotation = (this.currentPiece.rotation + 1) % 4;
        const rotationKey = `${this.currentPiece.rotation}->${newRotation}`;
        const kicks = this.wallKicks[rotationKey];

        // 首先尝试原地旋转
        if (!this.isCollision(this.currentPiece, 0, 0, newRotation)) {
            this.currentPiece.rotation = newRotation;
            this.currentPiece.shape = this.getRotatedShape(this.pieces[this.currentPiece.type], newRotation);
            this.playSound('rotate');
        } else {
            // 尝试墙踢
            for (let kick of kicks) {
                if (!this.isCollision(this.currentPiece, kick[0], kick[1], newRotation)) {
                    this.currentPiece.rotation = newRotation;
                    this.currentPiece.shape = this.getRotatedShape(this.pieces[this.currentPiece.type], newRotation);
                    this.currentPiece.x += kick[0];
                    this.currentPiece.y += kick[1];
                    this.playSound('rotate');
                    break;
                }
            }
        }

        this.draw();
    }

    // 直接落下
    hardDrop() {
        let dropDistance = 0;
        while (!this.isCollision(this.currentPiece, 0, 1)) {
            this.currentPiece.y++;
            dropDistance++;
        }

        // 计算分数
        this.score += dropDistance * 2;
        this.playSound('drop');
        this.createDropEffect();
        this.lockPiece();
        this.updateDisplay();
    }

    // Hold功能
    holdPiece() {
        if (!this.canHold) return;

        if (this.heldPiece === null) {
            this.heldPiece = {
                type: this.currentPiece.type,
                shape: this.pieces[this.currentPiece.type],
                rotation: 0
            };
            this.spawnPiece();
        } else {
            // 交换当前方块和held方块
            const temp = this.heldPiece;
            this.heldPiece = {
                type: this.currentPiece.type,
                shape: this.pieces[this.currentPiece.type],
                rotation: 0
            };

            this.currentPiece = {
                ...temp,
                x: Math.floor(this.boardWidth / 2) - Math.floor(temp.shape[0].length / 2),
                y: 0
            };
        }

        this.canHold = false;
        this.playSound('hold');
        this.draw();
    }

    // 锁定方块到游戏板
    lockPiece() {
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardY = this.currentPiece.y + y;
                    const boardX = this.currentPiece.x + x;

                    if (boardY >= 0) {
                        this.board[boardY][boardX] = this.currentPiece.type;
                    }
                }
            }
        }

        // 清除完整的行
        this.clearLines();

        // 生成新方块
        this.spawnPiece();
        this.draw();
    }

    // 清除完整的行
    clearLines() {
        let linesCleared = 0;
        const clearedLines = [];

        for (let y = this.boardHeight - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                clearedLines.push(y);
                this.board.splice(y, 1);
                this.board.unshift(new Array(this.boardWidth).fill(0));
                linesCleared++;
                y++; // 重新检查当前行
            }
        }

        // 更新分数和等级
        if (linesCleared > 0) {
            this.lines += linesCleared;
            this.stats.totalLines += linesCleared;

            // 计算消除分数
            const lineScores = [0, 100, 300, 500, 800];
            this.score += lineScores[linesCleared] * this.level;

            // 更新等级
            this.level = Math.floor(this.lines / 10) + 1;

            // 更新下降速度
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);

            // 创建消除效果
            this.createLineClearEffect(clearedLines);
            this.playSound('clear');

            this.updateDisplay();
        }
    }

    // 创建消除行效果
    createLineClearEffect(lines) {
        lines.forEach(line => {
            for (let x = 0; x < this.boardWidth; x++) {
                this.createParticle(
                    x * this.blockSize + this.blockSize / 2,
                    line * this.blockSize + this.blockSize / 2,
                    this.colors[this.board[line][x]]
                );
            }
        });
    }

    // 创建下落效果
    createDropEffect() {
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    this.createParticle(
                        (this.currentPiece.x + x) * this.blockSize + this.blockSize / 2,
                        (this.currentPiece.y + y) * this.blockSize + this.blockSize / 2,
                        this.colors[this.currentPiece.type]
                    );
                }
            }
        }
    }

    // 创建粒子效果
    createParticle(x, y, color) {
        this.particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 4,
            vy: (Math.random() - 0.5) * 4,
            color: color,
            life: 1.0,
            decay: 0.02
        });
    }

    // 更新粒子效果
    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= particle.decay;

            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    // 绘制粒子效果
    drawParticles() {
        this.particles.forEach(particle => {
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.fillRect(particle.x - 2, particle.y - 2, 4, 4);
        });
        this.ctx.globalAlpha = 1.0;
    }

    // 绘制游戏画面
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        if (this.settings.gridEnabled) {
            this.drawGrid();
        }

        // 绘制已锁定的方块
        for (let y = 0; y < this.boardHeight; y++) {
            for (let x = 0; x < this.boardWidth; x++) {
                if (this.board[y][x] !== 0) {
                    this.drawBlock(x, y, this.colors[this.board[y][x]]);
                }
            }
        }

        // 绘制当前方块
        if (this.currentPiece) {
            if (this.settings.ghostPieceEnabled) {
                this.drawGhostPiece();
            }
            for (let y = 0; y < this.currentPiece.shape.length; y++) {
                for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                    if (this.currentPiece.shape[y][x]) {
                        this.drawBlock(
                            this.currentPiece.x + x,
                            this.currentPiece.y + y,
                            this.colors[this.currentPiece.type]
                        );
                    }
                }
            }
        }

        // 绘制粒子效果
        this.drawParticles();
    }

    // 绘制网格
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        for (let x = 0; x <= this.boardWidth; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.blockSize, 0);
            this.ctx.lineTo(x * this.blockSize, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.boardHeight; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.blockSize);
            this.ctx.lineTo(this.canvas.width, y * this.blockSize);
            this.ctx.stroke();
        }
    }

    // 绘制单个方块
    drawBlock(x, y, color) {
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, this.blockSize, this.blockSize);

        // 添加高光效果
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, this.blockSize, 2);
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, 2, this.blockSize);

        // 添加阴影效果
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(x * this.blockSize + this.blockSize - 2, y * this.blockSize, 2, this.blockSize);
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize + this.blockSize - 2, this.blockSize, 2);

        // 添加边框
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x * this.blockSize, y * this.blockSize, this.blockSize, this.blockSize);
    }

    // 绘制幽灵方块（显示落点）
    drawGhostPiece() {
        let ghostY = this.currentPiece.y;
        while (!this.isCollision(this.currentPiece, 0, ghostY - this.currentPiece.y + 1)) {
            ghostY++;
        }

        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                    this.ctx.fillRect(
                        (this.currentPiece.x + x) * this.blockSize,
                        (ghostY + y) * this.blockSize,
                        this.blockSize,
                        this.blockSize
                    );
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(
                        (this.currentPiece.x + x) * this.blockSize,
                        (ghostY + y) * this.blockSize,
                        this.blockSize,
                        this.blockSize
                    );
                }
            }
        }
    }

    // 绘制下一个方块
    drawNextPiece() {
        this.nextCtx.fillStyle = '#000';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        if (this.nextPiece) {
            const blockSize = 20;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

            for (let y = 0; y < this.nextPiece.shape.length; y++) {
                for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                    if (this.nextPiece.shape[y][x]) {
                        this.nextCtx.fillStyle = this.colors[this.nextPiece.type];
                        this.nextCtx.fillRect(offsetX + x * blockSize, offsetY + y * blockSize, blockSize, blockSize);

                        this.nextCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(offsetX + x * blockSize, offsetY + y * blockSize, blockSize, blockSize);
                    }
                }
            }
        }
    }

    // 更新显示
    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;

        this.stats.totalScore = this.score;
    }

    // 游戏循环
    gameLoop() {
        if (!this.gameOver) {
            const now = Date.now();

            if (now - this.dropTime > this.dropInterval) {
                if (!this.isPaused && this.isStarted) {
                    this.movePiece(0, 1);
                }
                this.dropTime = now;
            }

            // 更新粒子效果
            this.updateParticles();

            requestAnimationFrame(() => this.gameLoop());
        }
    }

    // 显示游戏结束
    showGameOver() {
        document.getElementById('statusMessage').textContent = '游戏结束！';
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('startBtn').disabled = true;
        this.playSound('gameOver');
    }

    // 显示设置
    showSettings() {
        // 简单的设置切换
        this.settings.soundEnabled = !this.settings.soundEnabled;
        this.settings.ghostPieceEnabled = !this.settings.ghostPieceEnabled;
        this.settings.gridEnabled = !this.settings.gridEnabled;

        this.saveSettings();
        alert(`设置已更新:\n音效: ${this.settings.soundEnabled ? '开' : '关'}\n幽灵方块: ${this.settings.ghostPieceEnabled ? '开' : '关'}\n网格: ${this.settings.gridEnabled ? '开' : '关'}`);
    }

    // 显示统计
    showStats() {
        const statsText = `
游戏统计:
总游戏次数: ${this.stats.gamesPlayed}
最高分数: ${this.stats.bestScore}
总方块数: ${this.stats.totalPieces}
总消除行数: ${this.stats.totalLines}
游戏时间: ${Math.floor(this.stats.playTime / 1000)}秒
        `;
        alert(statsText);
    }

    // 保存设置
    saveSettings() {
        try {
            localStorage.setItem('tetrisSettings', JSON.stringify(this.settings));
        } catch (e) {
            console.log('保存设置失败:', e);
        }
    }

    // 加载设置
    loadSettings() {
        try {
            const saved = localStorage.getItem('tetrisSettings');
            if (saved) {
                this.settings = { ...this.settings, ...JSON.parse(saved) };
            }
        } catch (e) {
            console.log('加载设置失败:', e);
        }
    }

    // 保存统计
    saveStats() {
        try {
            localStorage.setItem('tetrisStats', JSON.stringify(this.stats));
        } catch (e) {
            console.log('保存统计失败:', e);
        }
    }

    // 加载统计
    loadStats() {
        try {
            const saved = localStorage.getItem('tetrisStats');
            if (saved) {
                const loadedStats = JSON.parse(saved);
                this.stats = { ...this.stats, ...loadedStats };
            }
        } catch (e) {
            console.log('加载统计失败:', e);
        }
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new TetrisGame();
});