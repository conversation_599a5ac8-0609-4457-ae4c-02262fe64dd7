// 俄罗斯方块核心游戏逻辑实现
class TetrisGame {
    constructor() {
        // Canvas设置
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.nextCanvas = document.getElementById('nextCanvas');
        this.nextCtx = this.nextCanvas.getContext('2d');

        // 游戏板设置
        this.boardWidth = 10;
        this.boardHeight = 20;
        this.blockSize = 30;

        // 游戏状态
        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.heldPiece = null;
        this.canHold = true;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameOver = false;
        this.isPaused = false;
        this.dropTime = 0;
        this.dropInterval = 1000;

        // 方块颜色定义
        this.colors = {
            'I': '#00f0f0',  // 青色
            'O': '#f0f000',  // 黄色
            'T': '#a000f0',  // 紫色
            'S': '#00f000',  // 绿色
            'Z': '#f00000',  // 红色
            'J': '#0000f0',  // 蓝色
            'L': '#f0a000'   // 橙色
        };

        // 7种标准俄罗斯方块形状定义
        this.pieces = {
            'I': [
                [1, 1, 1, 1]
            ],
            'O': [
                [1, 1],
                [1, 1]
            ],
            'T': [
                [0, 1, 0],
                [1, 1, 1]
            ],
            'S': [
                [0, 1, 1],
                [1, 1, 0]
            ],
            'Z': [
                [1, 1, 0],
                [0, 1, 1]
            ],
            'J': [
                [1, 0, 0],
                [1, 1, 1]
            ],
            'L': [
                [0, 0, 1],
                [1, 1, 1]
            ]
        };

        // 墙踢数据（Super Rotation System）
        this.wallKicks = {
            '0->1': [[0, 0], [-1, 0], [-1, 1], [0, -2], [-1, -2]],
            '1->0': [[0, 0], [1, 0], [1, -1], [0, 2], [1, 2]],
            '1->2': [[0, 0], [1, 0], [1, -1], [0, 2], [1, 2]],
            '2->1': [[0, 0], [-1, 0], [-1, 1], [0, -2], [-1, -2]],
            '2->3': [[0, 0], [1, 0], [1, 1], [0, -2], [1, -2]],
            '3->2': [[0, 0], [-1, 0], [-1, -1], [0, 2], [-1, 2]],
            '3->0': [[0, 0], [-1, 0], [-1, -1], [0, 2], [-1, 2]],
            '0->3': [[0, 0], [1, 0], [1, 1], [0, -2], [1, -2]]
        };

        this.pieceTypes = Object.keys(this.pieces);

        // 初始化游戏
        this.init();
    }

    init() {
        this.initBoard();
        this.setupEventListeners();
        this.spawnPiece();
        this.updateDisplay();
        this.gameLoop();
    }

    // 初始化游戏板
    initBoard() {
        for (let y = 0; y < this.boardHeight; y++) {
            this.board[y] = new Array(this.boardWidth).fill(0);
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.handleKeyPress(e));

        // 按钮事件
        document.getElementById('startBtn').addEventListener('click', () => this.start());
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());
    }

    // 生成新方块
    spawnPiece() {
        if (this.nextPiece === null) {
            this.nextPiece = this.getRandomPiece();
        }

        this.currentPiece = this.nextPiece;
        this.nextPiece = this.getRandomPiece();

        // 设置方块初始位置
        this.currentPiece.x = Math.floor(this.boardWidth / 2) - Math.floor(this.currentPiece.shape[0].length / 2);
        this.currentPiece.y = 0;
        this.currentPiece.rotation = 0;

        // 检查游戏是否结束
        if (this.isCollision(this.currentPiece, 0, 0)) {
            this.gameOver = true;
            this.showGameOver();
        }

        // 重置hold权限
        this.canHold = true;

        this.drawNextPiece();
    }

    // 获取随机方块
    getRandomPiece() {
        const type = this.pieceTypes[Math.floor(Math.random() * this.pieceTypes.length)];
        const shape = this.pieces[type];

        return {
            shape: shape,
            type: type,
            x: 0,
            y: 0,
            rotation: 0
        };
    }

    // 碰撞检测算法
    isCollision(piece, dx, dy, rotation = piece.rotation) {
        const newX = piece.x + dx;
        const newY = piece.y + dy;

        // 获取旋转后的形状
        let testShape = this.getRotatedShape(piece.shape, rotation);

        for (let y = 0; y < testShape.length; y++) {
            for (let x = 0; x < testShape[y].length; x++) {
                if (testShape[y][x]) {
                    const boardX = newX + x;
                    const boardY = newY + y;

                    // 检查边界
                    if (boardX < 0 || boardX >= this.boardWidth ||
                        boardY >= this.boardHeight) {
                        return true;
                    }

                    // 检查与已锁定方块的碰撞
                    if (boardY >= 0 && this.board[boardY][boardX] !== 0) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 获取旋转后的形状
    getRotatedShape(shape, rotation) {
        let rotated = shape;

        for (let i = 0; i < rotation; i++) {
            rotated = this.rotateMatrix(rotated);
        }

        return rotated;
    }

    // 矩阵旋转算法
    rotateMatrix(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const rotated = [];

        for (let x = 0; x < cols; x++) {
            rotated[x] = [];
            for (let y = rows - 1; y >= 0; y--) {
                rotated[x][rows - 1 - y] = matrix[y][x];
            }
        }

        return rotated;
    }

    // 处理键盘输入
    handleKeyPress(e) {
        if (this.gameOver) return;

        switch(e.key) {
            case 'ArrowLeft':
                if (!this.isPaused) this.movePiece(-1, 0);
                break;
            case 'ArrowRight':
                if (!this.isPaused) this.movePiece(1, 0);
                break;
            case 'ArrowDown':
                if (!this.isPaused) this.movePiece(0, 1);
                break;
            case 'ArrowUp':
                if (!this.isPaused) this.rotatePiece();
                break;
            case ' ':
                if (!this.isPaused) this.hardDrop();
                break;
            case 'c':
            case 'C':
                if (!this.isPaused) this.holdPiece();
                break;
        }
    }

    // 移动方块
    movePiece(dx, dy) {
        if (!this.isCollision(this.currentPiece, dx, dy)) {
            this.currentPiece.x += dx;
            this.currentPiece.y += dy;

            // 如果是向下移动，增加分数
            if (dy > 0) {
                this.score += 1;
                this.updateDisplay();
            }

            this.draw();
        } else if (dy > 0) {
            // 向下移动时发生碰撞，锁定方块
            this.lockPiece();
        }
    }

    // 旋转方块（使用Super Rotation System）
    rotatePiece() {
        const newRotation = (this.currentPiece.rotation + 1) % 4;
        const rotationKey = `${this.currentPiece.rotation}->${newRotation}`;
        const kicks = this.wallKicks[rotationKey];

        // 首先尝试原地旋转
        if (!this.isCollision(this.currentPiece, 0, 0, newRotation)) {
            this.currentPiece.rotation = newRotation;
            this.currentPiece.shape = this.getRotatedShape(this.pieces[this.currentPiece.type], newRotation);
        } else {
            // 尝试墙踢
            for (let kick of kicks) {
                if (!this.isCollision(this.currentPiece, kick[0], kick[1], newRotation)) {
                    this.currentPiece.rotation = newRotation;
                    this.currentPiece.shape = this.getRotatedShape(this.pieces[this.currentPiece.type], newRotation);
                    this.currentPiece.x += kick[0];
                    this.currentPiece.y += kick[1];
                    break;
                }
            }
        }

        this.draw();
    }

    // 直接落下
    hardDrop() {
        let dropDistance = 0;
        while (!this.isCollision(this.currentPiece, 0, 1)) {
            this.currentPiece.y++;
            dropDistance++;
        }

        // 计算分数
        this.score += dropDistance * 2;
        this.lockPiece();
        this.updateDisplay();
    }

    // Hold功能
    holdPiece() {
        if (!this.canHold) return;

        if (this.heldPiece === null) {
            this.heldPiece = {
                type: this.currentPiece.type,
                shape: this.pieces[this.currentPiece.type],
                rotation: 0
            };
            this.spawnPiece();
        } else {
            // 交换当前方块和held方块
            const temp = this.heldPiece;
            this.heldPiece = {
                type: this.currentPiece.type,
                shape: this.pieces[this.currentPiece.type],
                rotation: 0
            };

            this.currentPiece = {
                ...temp,
                x: Math.floor(this.boardWidth / 2) - Math.floor(temp.shape[0].length / 2),
                y: 0
            };
        }

        this.canHold = false;
        this.draw();
    }

    // 锁定方块到游戏板
    lockPiece() {
        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    const boardY = this.currentPiece.y + y;
                    const boardX = this.currentPiece.x + x;

                    if (boardY >= 0) {
                        this.board[boardY][boardX] = this.currentPiece.type;
                    }
                }
            }
        }

        // 清除完整的行
        this.clearLines();

        // 生成新方块
        this.spawnPiece();
        this.draw();
    }

    // 清除完整的行
    clearLines() {
        let linesCleared = 0;

        for (let y = this.boardHeight - 1; y >= 0; y--) {
            if (this.board[y].every(cell => cell !== 0)) {
                // 移除完整的行
                this.board.splice(y, 1);
                // 在顶部添加新的空行
                this.board.unshift(new Array(this.boardWidth).fill(0));
                linesCleared++;
                y++; // 重新检查当前行
            }
        }

        // 更新分数和等级
        if (linesCleared > 0) {
            this.lines += linesCleared;

            // 计算消除分数
            const lineScores = [0, 100, 300, 500, 800];
            this.score += lineScores[linesCleared] * this.level;

            // 更新等级
            this.level = Math.floor(this.lines / 10) + 1;

            // 更新下降速度
            this.dropInterval = Math.max(100, 1000 - (this.level - 1) * 100);

            this.updateDisplay();
        }
    }

    // 绘制游戏画面
    draw() {
        // 清空画布
        this.ctx.fillStyle = '#000';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制网格
        this.drawGrid();

        // 绘制已锁定的方块
        for (let y = 0; y < this.boardHeight; y++) {
            for (let x = 0; x < this.boardWidth; x++) {
                if (this.board[y][x] !== 0) {
                    this.drawBlock(x, y, this.colors[this.board[y][x]]);
                }
            }
        }

        // 绘制当前方块
        if (this.currentPiece) {
            this.drawGhostPiece();
            for (let y = 0; y < this.currentPiece.shape.length; y++) {
                for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                    if (this.currentPiece.shape[y][x]) {
                        this.drawBlock(
                            this.currentPiece.x + x,
                            this.currentPiece.y + y,
                            this.colors[this.currentPiece.type]
                        );
                    }
                }
            }
        }
    }

    // 绘制网格
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;

        for (let x = 0; x <= this.boardWidth; x++) {
            this.ctx.beginPath();
            this.ctx.moveTo(x * this.blockSize, 0);
            this.ctx.lineTo(x * this.blockSize, this.canvas.height);
            this.ctx.stroke();
        }

        for (let y = 0; y <= this.boardHeight; y++) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y * this.blockSize);
            this.ctx.lineTo(this.canvas.width, y * this.blockSize);
            this.ctx.stroke();
        }
    }

    // 绘制单个方块
    drawBlock(x, y, color) {
        this.ctx.fillStyle = color;
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, this.blockSize, this.blockSize);

        // 添加高光效果
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, this.blockSize, 2);
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize, 2, this.blockSize);

        // 添加阴影效果
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.fillRect(x * this.blockSize + this.blockSize - 2, y * this.blockSize, 2, this.blockSize);
        this.ctx.fillRect(x * this.blockSize, y * this.blockSize + this.blockSize - 2, this.blockSize, 2);

        // 添加边框
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(x * this.blockSize, y * this.blockSize, this.blockSize, this.blockSize);
    }

    // 绘制幽灵方块（显示落点）
    drawGhostPiece() {
        let ghostY = this.currentPiece.y;
        while (!this.isCollision(this.currentPiece, 0, ghostY - this.currentPiece.y + 1)) {
            ghostY++;
        }

        for (let y = 0; y < this.currentPiece.shape.length; y++) {
            for (let x = 0; x < this.currentPiece.shape[y].length; x++) {
                if (this.currentPiece.shape[y][x]) {
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                    this.ctx.fillRect(
                        (this.currentPiece.x + x) * this.blockSize,
                        (ghostY + y) * this.blockSize,
                        this.blockSize,
                        this.blockSize
                    );
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(
                        (this.currentPiece.x + x) * this.blockSize,
                        (ghostY + y) * this.blockSize,
                        this.blockSize,
                        this.blockSize
                    );
                }
            }
        }
    }

    // 绘制下一个方块
    drawNextPiece() {
        this.nextCtx.fillStyle = '#000';
        this.nextCtx.fillRect(0, 0, this.nextCanvas.width, this.nextCanvas.height);

        if (this.nextPiece) {
            const blockSize = 20;
            const offsetX = (this.nextCanvas.width - this.nextPiece.shape[0].length * blockSize) / 2;
            const offsetY = (this.nextCanvas.height - this.nextPiece.shape.length * blockSize) / 2;

            for (let y = 0; y < this.nextPiece.shape.length; y++) {
                for (let x = 0; x < this.nextPiece.shape[y].length; x++) {
                    if (this.nextPiece.shape[y][x]) {
                        this.nextCtx.fillStyle = this.colors[this.nextPiece.type];
                        this.nextCtx.fillRect(offsetX + x * blockSize, offsetY + y * blockSize, blockSize, blockSize);

                        this.nextCtx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
                        this.nextCtx.lineWidth = 1;
                        this.nextCtx.strokeRect(offsetX + x * blockSize, offsetY + y * blockSize, blockSize, blockSize);
                    }
                }
            }
        }
    }

    // 更新显示
    updateDisplay() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('level').textContent = this.level;
        document.getElementById('lines').textContent = this.lines;
    }

    // 游戏循环
    gameLoop() {
        if (!this.gameOver) {
            const now = Date.now();

            if (now - this.dropTime > this.dropInterval) {
                if (!this.isPaused) {
                    this.movePiece(0, 1);
                }
                this.dropTime = now;
            }

            requestAnimationFrame(() => this.gameLoop());
        }
    }

    // 开始游戏
    start() {
        if (this.gameOver) {
            this.reset();
        } else {
            this.isPaused = false;
            document.getElementById('pauseBtn').textContent = '暂停';
            document.getElementById('statusMessage').textContent = '游戏进行中';
        }
    }

    // 切换暂停
    togglePause() {
        if (!this.gameOver) {
            this.isPaused = !this.isPaused;
            document.getElementById('pauseBtn').textContent = this.isPaused ? '继续' : '暂停';
            document.getElementById('statusMessage').textContent = this.isPaused ? '游戏暂停' : '游戏进行中';
        }
    }

    // 重置游戏
    reset() {
        this.board = [];
        this.currentPiece = null;
        this.nextPiece = null;
        this.heldPiece = null;
        this.canHold = true;
        this.score = 0;
        this.level = 1;
        this.lines = 0;
        this.gameOver = false;
        this.isPaused = false;
        this.dropTime = 0;
        this.dropInterval = 1000;

        this.initBoard();
        this.spawnPiece();
        this.updateDisplay();

        document.getElementById('statusMessage').textContent = '按开始游戏开始';
        document.getElementById('pauseBtn').textContent = '暂停';
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('startBtn').disabled = false;
    }

    // 显示游戏结束
    showGameOver() {
        document.getElementById('statusMessage').textContent = '游戏结束！';
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('startBtn').disabled = true;
    }
}

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    const game = new TetrisGame();
});