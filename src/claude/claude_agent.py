import re
import os
from claude_code_sdk import ClaudeSDKClient, ClaudeCodeOptions,ResultMessage, HookContext,HookMatcher
from claude_code_sdk.types import Message, AssistantMessage, ResultMessage, PermissionResult, PermissionResultAllow, PermissionResultDeny, ToolPermissionContext,HookJSONOutput,TextBlock,UserMessage,StreamEvent,ThinkingBlock,ToolUseBlock,ToolResultBlock
from typing import Dict, List, Optional, Any, Callable, Union
from loguru import logger
import json

class ClaudeAgent:
    """ClaudeCode 智能体"""
    def __init__(self, system_prompt:str, provider = "local", maxt_tokens=15000):
        self.system_prompt = system_prompt
        self.maxt_tokens = maxt_tokens
        if provider == "zhipu":
            self.set_zhipu_model()
        elif provider == "local":
            self.set_local_model()
        elif provider == "claude":
            self.set_claude_model()
    
    def set_zhipu_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://open.bigmodel.cn/api/anthropic"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = "69a9bf45e4084febb7c63c34b74c6afa.NQEUZXulrv0N17Rg"
        os.environ["ANTHROPIC_MODEL"] = ""
        os.environ["ANTHROPIC_API_KEY"] = ""
    
    def set_claude_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://api.aicodemirror.com/api/claudecode"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = ""
        os.environ["ANTHROPIC_MODEL"] = ""
        os.environ["ANTHROPIC_API_KEY"] = "sk-ant-api03-ltc4tIgTW6CjHSJ-MSl5lSjYVfHYOyy-r219vjK-n4UqMkq_iJRclyXdNGBN0ySciQpzGQXB5Ul6wTGnTBu15Q"
        
    def set_local_model(self):
        os.environ["ANTHROPIC_BASE_URL"] = "https://ai.secsign.online:3006/"
        os.environ["ANTHROPIC_AUTH_TOKEN"] = "sk-NO_KEY"
        os.environ["ANTHROPIC_MODEL"] = "glm-4.5-air"
        os.environ["ANTHROPIC_API_KEY"] = ""
        
    async def run_agent(self, work_dir , user_req, session_id :str = None, nothink="", progress_callback=None
    ) -> Dict[str, Any]:
        if session_id and session_id.strip().lower() == "none":
            session_id = None
        
        def claude_progress(progress: int, title:str, message: str=''):
            #print(f"进度: {progress}% {title}:{message}")
            if progress_callback:
                message = message.replace("\n", "↵ ")
                progress_callback(progress, title, message)
        async def file_permission_handler(
            tool_name: str,
            input_data: dict,
            context: ToolPermissionContext
        ) -> PermissionResultAllow | PermissionResultDeny:
                """工具权限的自定义逻辑。"""
                #print(f"{tool_name} 权限请求")

                # 阻止读写工作目录之外的文件
                if tool_name in ["Write","Read","mcp__read_file__read_file"]:
                    file_path = input_data.get("file_path", "")
                    real_path = os.path.abspath(file_path)
                    real_dir = os.path.dirname(real_path)
                    if not real_dir.startswith(work_dir):
                        print(f"⚠️ {tool_name} 不能读写文件: {real_path}")
                        return PermissionResultDeny(
                            behavior="deny",
                            message=f"只能读写工作目录: {work_dir}.",
                            interrupt=True
                        )
                # 允许其他所有操作
                return PermissionResultAllow(
                    behavior = "allow",
                    updated_input=input_data
                )
        
        result = {
            "is_error":False
        }
        options=ClaudeCodeOptions(
                append_system_prompt = self.system_prompt,
                resume = session_id,
                #max_turns=200,
                cwd=work_dir,
                permission_mode="acceptEdits",
                #mcp_servers=self.mcp_servers,
                allowed_tools=["Write","Bash","Edit","Grep","mcp__read_file__read_file"],
                disallowed_tools=["Read"],
                #settings=os.path.join(work_dir, ".claude/settings.local.json"),
                extra_args={
                    #"verbose": None,
                    #"output-format": "stream-json",
                    #"mcp-debug": None,
                    #"dangerously-skip-permissions": None
                },
                can_use_tool = file_permission_handler,
                # hooks={
                #     'PreToolUse': [
                #         #HookMatcher(matcher='Bash', hooks=[validate_file_path_command]),
                #         HookMatcher(hooks=[validate_file_path_command]),  # 适用于所有工具
                #     ]
                # }
        )
        def print_message_content(message_count, title, content: list[Any], claude_progress):
            try:
                for block in content:
                    if isinstance(block, TextBlock):
                        claude_progress(message_count,title, f"💬 {block.text}")
                    elif isinstance(block, ThinkingBlock):
                        claude_progress(message_count,title, f"🤔 {block.thinking}")
                    elif isinstance(block, ToolUseBlock):
                        #input_str = f"{block.input}"[:100].replace("\n", "↵ ") #⏎ ¶
                        input_str = block.input
                        claude_progress(message_count,title, f"🔧 {block.name}, {input_str}")
                    elif isinstance(block, ToolResultBlock):
                        tool_rst = f"{block.content}"
                        # if not "failed" in tool_rst and not "Error" in tool_rst:
                        #     tool_rst = tool_rst[:50]
                        claude_progress(message_count,title, f"📊 {tool_rst}")
            except Exception as e:
                print(f"ERROR: {e}")
            
        new_session_id = session_id
        result["is_error"] = False
        message_count = 0
        async with ClaudeSDKClient(options) as client:
            claude_progress(0,"Request", f"📝 Sending query to Claude : {user_req}")
            await client.query(f"{user_req} {nothink}")
            async for message in client.receive_response():
                message_count += 1
                if isinstance(message, AssistantMessage):
                    # Print Claude's text responses
                    print_message_content(message_count,"Assistant", message.content, claude_progress)
                elif isinstance(message, UserMessage):
                    print_message_content(message_count,"User", message.content, claude_progress)
                elif isinstance(message, StreamEvent):
                    # Print Claude's text responses
                    for event in message.event:
                        claude_progress(message_count, "Stream",  f"🌊 {event}")
                elif isinstance(message, ResultMessage):
                    claude_progress(100, "Result", f"✅ Task completed! Duration: {message.duration_ms/1000} seconds.")
                    result["is_error"] = message.is_error
                    result["message"] = f"{message.result}"
                    new_session_id = message.session_id
        
        if result["is_error"]:
            print(f"ERROR: {result}")
        result["success"] = not result["is_error"]
        result["session_id"] = new_session_id
        return result    

async def validate_file_path_hook(
    input_data: dict[str, Any],
    tool_use_id: str | None,
    context: HookContext
) -> HookJSONOutput:
    """验证并阻止危险的 bash 命令。"""
    if input_data['tool_name'] == 'Read' or input_data['tool_name'] == 'Write' or input_data['tool_name'] == 'mcp__read_file__read_file':
        
        file_path = input_data.get("file_path", "")
        if file_path.startswith("/system/"):
            return {
                'hookSpecificOutput': {
                    'hookEventName': 'PreToolUse',
                    'permissionDecision': 'deny',
                    'permissionDecisionReason': '危险命令已阻止'
                }
            }
    return {}