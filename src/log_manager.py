import os
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LLMLogEntry:
    """LLM交互日志条目"""
    def __init__(self, timestamp: str, progress: str, log_type: str, content: str):
        self.timestamp = timestamp
        self.progress = progress
        self.log_type = log_type  # Assistant, User, Result, Stream, Request
        self.content = content

    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp,
            "progress": self.progress,
            "log_type": self.log_type,
            "content": self.content
        }

class LLMLogManager:
    """LLM交互日志管理器"""

    def __init__(self, project_name: str, task_id: str):
        self.project_name = project_name
        self.task_id = task_id

        # 创建日志目录
        self.log_dir = os.path.join("data", "logs", project_name)
        os.makedirs(self.log_dir, exist_ok=True)

        # LLM交互日志文件
        self.llm_log_file = os.path.join(self.log_dir, f"task_{task_id}.log")

        # 初始化日志条目列表
        self.log_entries: List[LLMLogEntry] = []

        # 加载现有日志
        self._load_existing_logs()

    def _load_existing_logs(self):
        """加载现有的LLM日志"""
        if os.path.exists(self.llm_log_file):
            try:
                with open(self.llm_log_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:
                            entry = self._parse_log_line(line)
                            if entry:
                                self.log_entries.append(entry)
            except Exception as e:
                logging.error(f"加载LLM日志失败: {e}")

    def _parse_log_line(self, line: str) -> Optional[LLMLogEntry]:
        """解析日志行"""
        try:
            # 格式: [2025-09-27 21:44:09] 0% - Assistant: 💬 content
            import re
            pattern = r'\[([^\]]+)\] ([^-]+) - ([^:]+): (.+)'
            match = re.match(pattern, line)
            if match:
                timestamp = match.group(1)
                progress = match.group(2).strip()
                log_type = match.group(3).strip()
                content = match.group(4).strip()
                return LLMLogEntry(timestamp, progress, log_type, content)
        except Exception:
            pass
        return None

    def add_log_entry(self, progress: str, log_type: str, content: str):
        """添加LLM日志条目"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        entry = LLMLogEntry(timestamp, progress, log_type, content)
        self.log_entries.append(entry)

        # 写入文件
        self._write_to_file(entry)

    def _write_to_file(self, entry: LLMLogEntry):
        """写入日志到文件"""
        try:
            with open(self.llm_log_file, 'a', encoding='utf-8') as f:
                log_line = f"[{entry.timestamp}] {entry.progress} - {entry.log_type}: {entry.content}\n"
                f.write(log_line)
        except Exception as e:
            logging.error(f"写入LLM日志失败: {e}")

    def get_logs(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取LLM日志"""
        return [entry.to_dict() for entry in self.log_entries[-limit:]]

    def clear_logs(self):
        """清空日志"""
        self.log_entries.clear()
        if os.path.exists(self.llm_log_file):
            os.remove(self.llm_log_file)

class TaskLogManager:
    """任务执行日志管理器 - 简化版，使用标准logging"""

    def __init__(self, work_dir: str = None):
        if work_dir is None:
            work_dir = os.path.join(os.getcwd(), "data")

        self.work_dir = work_dir
        self.logs_dir = os.path.join(work_dir, "logs")
        os.makedirs(self.logs_dir, exist_ok=True)

        # 设置日志文件
        self.log_file = os.path.join(self.logs_dir, "task_execution.log")

        # 初始化日志记录器
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        self.logger = logging.getLogger(f"TaskManager_{self.work_dir}")
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    # 兼容接口 - 现在直接使用标准logging
    def log_event(self, req_id: str, event_type: str, message: str,
                  level: str = "INFO", task_id: str = None, **kwargs):
        """记录事件（兼容接口）- 现在使用标准logging"""
        log_message = f"[{req_id}] {event_type}: {message}"

        level = level.upper()
        if level == "DEBUG":
            logging.debug(log_message)
        elif level == "WARNING":
            logging.warning(log_message)
        elif level == "ERROR":
            logging.error(log_message)
        elif level == "CRITICAL":
            logging.critical(log_message)
        else:
            logging.info(log_message)

    def get_logs(self, req_id: str = None, **kwargs):
        """获取日志（兼容接口）"""
        # 返回空列表，因为现在使用标准logging
        return []

    def get_task_logs(self, task_id: Optional[int] = None, **kwargs):
        """获取任务日志（兼容接口）"""
        # 返回空列表，因为现在使用标准logging
        return []


def create_llm_log_manager(project_name: str, task_id: str) -> LLMLogManager:
    """创建LLM日志管理器"""
    return LLMLogManager(project_name, task_id)


def get_task_llm_logs(project_name: str, task_id: str, limit: int = 100) -> List[Dict[str, Any]]:
    """获取任务的LLM交互日志"""
    log_manager = LLMLogManager(project_name, task_id)
    return log_manager.get_logs(limit)
