import os
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class TaskLogManager:
    """任务执行日志管理器"""
    
    def __init__(self, work_dir: str = None):
        if work_dir is None:
            work_dir = os.path.join(os.getcwd(), "data")

        self.work_dir = work_dir
        self.logs_dir = os.path.join(work_dir, "logs")
        os.makedirs(self.logs_dir, exist_ok=True)

        # 设置日志文件
        self.log_file = os.path.join(self.logs_dir, "task_execution.log")
        self.json_log_file = os.path.join(self.logs_dir, "task_logs.json")

        # 初始化日志记录器
        self._setup_logger()
        
        # 初始化JSON日志存储
        self._init_json_logs()
    
    def _setup_logger(self):
        """设置日志记录器"""
        self.logger = logging.getLogger(f"TaskManager_{self.work_dir}")
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加handler
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)
    
    def _init_json_logs(self):
        """初始化JSON日志存储"""
        if not os.path.exists(self.json_log_file):
            initial_data = {
                "logs": [],
                "meta": {
                    "created_at": datetime.now().isoformat(),
                    "total_logs": 0
                }
            }
            with open(self.json_log_file, 'w', encoding='utf-8') as f:
                json.dump(initial_data, f, indent=2, ensure_ascii=False)
    
    def log_task_event(self, task_id: int, event_type: str, message: str, 
                      level: LogLevel = LogLevel.INFO, extra_data: Dict[str, Any] = None):
        """记录任务事件"""
        timestamp = datetime.now().isoformat()
        
        # 记录到标准日志
        log_message = f"Task-{task_id} [{event_type}]: {message}"
        getattr(self.logger, level.value.lower())(log_message)
        
        # 记录到JSON日志
        log_entry = {
            "timestamp": timestamp,
            "task_id": task_id,
            "event_type": event_type,
            "level": level.value,
            "message": message,
            "extra_data": extra_data or {}
        }
        
        self._append_json_log(log_entry)

    def log_event(self, req_id: str, event_type: str, message: str,
                  level: str = "INFO", task_id: str = None, **kwargs):
        """记录事件（兼容接口）"""
        try:
            log_level = LogLevel(level.upper())
        except ValueError:
            log_level = LogLevel.INFO

        # 使用req_id作为task_id
        self.log_task_event(req_id, event_type, message, log_level, kwargs)

    def get_logs(self, req_id: str = None, **kwargs):
        """获取日志（兼容接口）"""
        return self.get_task_logs(req_id, **kwargs)

    def _append_json_log(self, log_entry: Dict[str, Any]):
        """追加日志到JSON文件"""
        try:
            with open(self.json_log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data["logs"].append(log_entry)
            data["meta"]["total_logs"] = len(data["logs"])
            data["meta"]["last_updated"] = datetime.now().isoformat()
            
            with open(self.json_log_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"写入JSON日志失败: {e}")
    
    def get_task_logs(self, task_id: Optional[int] = None, 
                     event_type: Optional[str] = None,
                     level: Optional[LogLevel] = None,
                     limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务日志"""
        try:
            with open(self.json_log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logs = data.get("logs", [])
            
            # 过滤条件
            if task_id is not None:
                logs = [log for log in logs if log.get("task_id") == task_id]
            
            if event_type:
                logs = [log for log in logs if log.get("event_type") == event_type]
            
            if level:
                logs = [log for log in logs if log.get("level") == level.value]
            
            # 按时间倒序排列，返回最新的limit条
            logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return logs[:limit]
            
        except Exception as e:
            self.logger.error(f"读取日志失败: {e}")
            return []
    
    def clear_logs(self, task_id: Optional[int] = None):
        """清理日志"""
        try:
            with open(self.json_log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if task_id is not None:
                # 只清理指定任务的日志
                data["logs"] = [log for log in data["logs"] if log.get("task_id") != task_id]
            else:
                # 清理所有日志
                data["logs"] = []
            
            data["meta"]["total_logs"] = len(data["logs"])
            data["meta"]["last_updated"] = datetime.now().isoformat()
            
            with open(self.json_log_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            self.logger.error(f"清理日志失败: {e}")
    
    def get_log_summary(self) -> Dict[str, Any]:
        """获取日志摘要"""
        try:
            with open(self.json_log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logs = data.get("logs", [])
            
            # 统计信息
            summary = {
                "total_logs": len(logs),
                "log_levels": {},
                "event_types": {},
                "tasks": set(),
                "date_range": {"start": None, "end": None}
            }
            
            for log in logs:
                # 统计日志级别
                level = log.get("level", "INFO")
                summary["log_levels"][level] = summary["log_levels"].get(level, 0) + 1
                
                # 统计事件类型
                event_type = log.get("event_type", "unknown")
                summary["event_types"][event_type] = summary["event_types"].get(event_type, 0) + 1
                
                # 统计任务
                task_id = log.get("task_id")
                if task_id is not None:
                    summary["tasks"].add(task_id)
                
                # 时间范围
                timestamp = log.get("timestamp")
                if timestamp:
                    if not summary["date_range"]["start"] or timestamp < summary["date_range"]["start"]:
                        summary["date_range"]["start"] = timestamp
                    if not summary["date_range"]["end"] or timestamp > summary["date_range"]["end"]:
                        summary["date_range"]["end"] = timestamp
            
            summary["tasks"] = list(summary["tasks"])
            return summary
            
        except Exception as e:
            self.logger.error(f"获取日志摘要失败: {e}")
            return {}

def create_progress_callback(log_manager: TaskLogManager, task_id: int):
    """创建进度回调函数"""
    def progress_callback(message: str, level: str = "INFO", extra_data: Dict[str, Any] = None):
        try:
            log_level = LogLevel(level.upper())
        except ValueError:
            log_level = LogLevel.INFO
        
        log_manager.log_task_event(
            task_id=task_id,
            event_type="progress",
            message=message,
            level=log_level,
            extra_data=extra_data
        )
    
    return progress_callback
