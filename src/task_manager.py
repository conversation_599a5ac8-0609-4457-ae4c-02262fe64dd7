"""
任务管理器模块
实现任务的创建、管理、执行和监控功能
"""

import os
import json
import uuid
import shutil
import threading
import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    from .claude.claude_agent import ClaudeAgent
except ImportError:
    from claude.claude_agent import ClaudeAgent

class Task:
    """任务类"""
    def __init__(self, id: str, title: str, description: str,
                 priority: str = "medium", dependencies: List[str] = None,
                 status: str = "pending", details: str = "", testStrategy: str = ""):
        self.id = id  # 使用id而不是task_id，与demo/.taskai/task.json保持一致
        self.title = title
        self.description = description
        self.priority = priority
        self.dependencies = dependencies or []
        self.status = status  # pending, running, completed, failed
        self.details = details  # 任务详细信息
        self.testStrategy = testStrategy  # 测试策略
        self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
        self.result = ""
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'priority': self.priority,
            'dependencies': self.dependencies,
            'status': self.status,
            'details': self.details,
            'testStrategy': self.testStrategy,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'result': self.result
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        # 兼容两种格式：新格式使用id，旧格式使用task_id
        id = data.get('id')
        task = cls(
            id=id,
            title=data['title'],
            description=data['description'],
            priority=data.get('priority', 'medium'),
            dependencies=data.get('dependencies', []),
            status=data.get('status', 'pending'),
            details=data.get('details', ''),
            testStrategy=data.get('testStrategy', '')
        )
        task.created_at = data.get('created_at', task.created_at)
        task.updated_at = data.get('updated_at', task.updated_at)
        task.result = data.get('result', '')
        return task


class TaskManager:
    """任务管理器"""
    
    def __init__(self, project_name: str, work_dir:str, log_manager=None):
        self.project_name = project_name
        self.log_manager = log_manager
        self.tasks: Dict[str, Task] = {}
        self.meta = {}
        self.is_running = False
        self.stop_event = threading.Event()
        self.max_workers = 3  # 并发任务数
        
        # 设置任务文件路径
        self.work_dir = work_dir
        self.task_file_name = os.path.join(self.work_dir, ".taskai/task.json")
        self.load_tasks()
        
        # 初始化Claude配置
        self._setup_claude_config()
        
    def _setup_claude_config(self):
        """设置Claude配置文件 - 完成TODO任务"""
        try:
            # 创建.claude目录
            claude_dir = os.path.join(self.work_dir, ".claude")
            os.makedirs(claude_dir, exist_ok=True)
            
            # 复制CLAUDE.md
            claude_md_src = os.path.join(os.getcwd(), "prompts", "CLAUDE.md")
            claude_md_dst = os.path.join(claude_dir, "CLAUDE.md")
            if os.path.exists(claude_md_src):
                shutil.copy2(claude_md_src, claude_md_dst)
            
            # 复制后替换CLAUDE.md中的work_dir
            with open(claude_md_dst, "r", encoding="utf-8") as f:
                claude_md = f.read()
                claude_md = claude_md.replace("{work_dir}", self.work_dir)
            # 写入CLAUDE.md
            with open(claude_md_dst, 'w', encoding='utf-8') as f:
                f.write(claude_md)
            
            # 复制settings.local.json
            settings_src = os.path.join(os.getcwd(), "prompts", "settings.local.json")
            settings_dst = os.path.join(claude_dir, "settings.local.json")
            if os.path.exists(settings_src) and not os.path.exists(settings_dst):
                shutil.copy2(settings_src, settings_dst)
                
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(self.project_name, "setup_error", f"设置Claude配置失败: {e}", level="warning")
    def load_tasks(self):
        """从文件加载任务"""
        try:
            if not os.path.exists(self.task_file_name):
                return

            with open(self.task_file_name, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 清空现有任务
            self.tasks.clear()

            # 加载任务
            for task_data in data.get('tasks', []):
                task = Task.from_dict(task_data)
                self.tasks[task.id] = task
            
            self.meta = data.get('meta', {})

        except Exception as e:
            logging.error(f"加载任务文件失败: {e}")
    def get_session_id(self):
        """获取session_id"""
        if self.meta:
            return self.meta.get('session_id', '')
        else:
            return None
    def set_session_id(self, session_id: str):
        """设置session_id"""
        if not self.meta:
            self.meta = {}
        
        self.meta['session_id'] = session_id
        self.save_tasks()
    def save_tasks(self):
        """保存任务到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.task_file_name), exist_ok=True)

            # 准备任务数据
            tasks_data = {
                "meta": self.meta,
                "tasks": [task.to_dict() for task in self.tasks.values()]
            }

            # 保存到文件
            with open(self.task_file_name, 'w', encoding='utf-8') as f:
                json.dump(tasks_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logging.error(f"保存任务文件失败: {e}")
            raise e
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任务(从文件加载)"""
        try:
            if not self.tasks:
                self.load_tasks()
                        
            return [task.to_dict() for task in self.tasks.values()]
        except Exception as e:
            logging.error(f"加载任务文件失败: {e}")
            return []
    def add_task(self, title: str, description: str, priority: str = "medium", dependencies: List[str] = None) -> str:
        """添加新任务"""
        try:
            # 使用递增的数字ID而不是UUID
            existing_ids = [int(task.id) for task in self.tasks.values() if str(task.id).isdigit()]
            task_id = str(max(existing_ids) + 1 if existing_ids else 1)

            # 创建任务对象
            task = Task(
                task_id=task_id,
                title=title,
                description=description,
                priority=priority,
                dependencies=dependencies or [],
                status="pending"
            )

            # 添加到任务列表
            self.tasks[task_id] = task

            # 保存任务
            self.save_tasks()

            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "task_added", f"添加新任务: {title}",
                    task_id=task_id, level="info"
                )

            return task_id

        except Exception as e:
            logging.error(f"添加任务失败: {e}")
            raise e
    def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        # 只有pending状态的任务可以修改基本信息
        if task.status != "pending" and any(k in kwargs for k in ['title', 'description', 'priority', 'dependencies']):
            return False
        
        for key, value in kwargs.items():
            if hasattr(task, key):
                setattr(task, key, value)
        
        task.updated_at = datetime.now().isoformat()
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_updated", f"更新任务: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def update_task_status(self, task_id: str, status: str, result: str = "", error_message: str = "") -> bool:
        """更新任务状态"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        old_status = task.status
        task.status = status
        task.updated_at = datetime.now().isoformat()
        
        if result:
            task.result = result
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_status_changed", 
                f"任务状态变更: {old_status} -> {status}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务（检查依赖关系）"""
        if task_id not in self.tasks:
            return False
        
        # 检查是否有其他任务依赖此任务
        dependent_tasks = [t for t in self.tasks.values() if task_id in t.dependencies]
        if dependent_tasks:
            return False  # 有依赖任务，不能删除
        
        task = self.tasks[task_id]
        del self.tasks[task_id]
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_deleted", f"删除任务: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def reset_task_status(self, task_id: str) -> bool:
        """重置任务状态"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        task.status = "pending"
        task.result = ""
        task.updated_at = datetime.now().isoformat()
        self.save_tasks()
        
        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_reset", f"重置任务状态: {task.title}",
                task_id=task_id, level="info"
            )
        
        return True
    
    def can_task_run(self, task_id: str) -> bool:
        """检查任务是否可以运行（依赖关系检查）"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        # 检查所有依赖任务是否已完成
        for dep_id in task.dependencies:
            if dep_id not in self.tasks:
                return False
            dep_task = self.tasks[dep_id]
            if dep_task.status != "completed":
                return False
        
        return True
    
    def load_prompt(self, prompt_file) -> str:
        with open(prompt_file, "r") as f:
            prompt = f.read()
        return prompt
    def gen_tasks(self, user_req, num_tasks=None, knowledge_base = None, progress_callback = None) -> Dict[str, Any]:        
        self.session_id = None
        # 加载prompts/task_split.md文件的提示词
        prompt_file = os.path.join("prompts", "gen_task.md")
        prompt = self.load_prompt(prompt_file)
        # 替换提示词中的占位符
        # 如果任务文件已经存在，则删除
        if os.path.exists(self.task_file_name):
            os.remove(self.task_file_name)
        prompt = prompt.replace("{task_file_name}", self.task_file_name)
        if num_tasks:
            prompt = prompt.replace("{num_tasks}", f"准确创建{num_tasks}个任务，编号从1到{num_tasks}")
        else:
            prompt = prompt.replace("{num_tasks}", "根据用户需求复杂性创建最佳数量的任务 - 使用您的判断力确定多少任务是合适的")
        
        result = {}
        try:
            logging.info('正在调用Claude Code 进行任务拆解...')          
            
        
            claude_agent = ClaudeAgent("你是一个需求任务拆解助手，不是代码实现助手", self.meta.get("provider"))
            user_req = prompt + user_req
            
            result = asyncio.run(claude_agent.run_agent(self.work_dir, user_req, self.session_id, "", progress_callback))
            if result.get("session_id"):
                self.session_id = result.get("session_id")
            if result.get("success"):
                logging.info(f'任务拆解完成，共{len(self.list_tasks())}个任务.')    
                # 提取任务，并持久化的work_dir的./tasks/tasks.json文件
                return result   
        except Exception as e:
            logging.error(f"任务拆解出错: {e}")
            result["success"] = False
            result["message"] = str(e)
        
        return result

    def run_single_task(self, task_id: str, progress_callback=None) -> Dict[str, Any]:
        task = self.tasks.get(task_id)
        if not task:
            return {'success': False, 'message': '任务不存在'}

        if task.status== "completed":
            return {'success': False, 'message': f'任务状态为{task.status}，无法运行'}

        if not self.can_task_run(task_id):
            return {'success': False, 'message': '任务依赖未满足，无法运行'}

        session_id = self.get_session_id()
        task_summary = ""
        if not session_id:
            # 如果是新会话，则总结已完成的任务
            task_summary = self._summarize_completed_tasks()

        return self._run_single_task(session_id, task, task_summary, progress_callback)
    def _run_single_task(self, session_id: str, task: Task, task_summary:str, progress_callback=None) -> Dict[str, Any]:
        """运行单个任务"""
        result = {}
        try:
            # 更新任务状态为进行中
            task.status = "in_progress"
            task.updated_at = datetime.now().isoformat()
            self.save_tasks()

            
            # 创建LLM日志管理器
            from log_manager import LLMLogManager
            llm_log_manager = LLMLogManager(self.project_name, task.id)

            # 清空之前的日志
            llm_log_manager.clear_logs()

            def log_and_callback(progress: int, title:str, content: str=''):
                # 使用LLM日志管理器记录日志
                llm_log_manager.add_log_entry(f"{progress}%", title, content)

                # 调用回调函数
                if progress_callback:
                    progress_callback(progress, title, content)
                else:
                    print(f"{progress}% - {title}: {content}")

            # 构建任务执行请求
            task_titles = [f"任务ID:{task.get('id')}\n任务标题:{task.get('title')}\n" for task in self.list_tasks()]
            task_titles = "\n".join(task_titles)
            user_req = f"""用户需求已经被拆解为如下任务:
            {task_titles}
            """
            claude_agent = ClaudeAgent("", self.meta.get("provider"))
            if task_summary:
                user_req += f"""\n已经完成的任务总结如下：
                {task_summary}
                """
            user_req += f"""\n
            请完成下面这个任务，并返回任务执行的总结。
            # 任务详情
            任务ID: {task.id}
            任务标题: {task.title}
            任务描述: {task.description}
            任务详情: {task.details}
            验证策略: {task.testStrategy}
            """
            
            claude_agent = ClaudeAgent("", self.meta.get("provider"))
            result = asyncio.run(claude_agent.run_agent(self.work_dir, user_req, session_id, "", log_and_callback))
            
            if result.get("session_id"):
                self.meta["session_id"] = result.get("session_id")
            
            if result.get("success"):
                task.status = "completed"
                task.result = result["message"]
            else:
                task.status = "failed"
                task.result = result["message"]
            self.save_tasks()
            
            return result
        except Exception as e:
            # 更新任务状态为失败
            task.status = "failed"
            task.result = str(e)
            self.save_tasks()
            
            logging.error(f"任务-[{task.title}]运行出错: {e}")
            result["success"] = False
            result["message"] = str(e)
            return result

    def get_task_logs(self, task_id: str) -> List[str]:
        """获取任务运行日志"""
        try:
            from log_manager import LLMLogManager
            llm_log_manager = LLMLogManager(self.project_name, task_id)

            # 获取LLM日志
            log_entries = llm_log_manager.get_logs(100)

            if not log_entries:
                return ["暂无日志记录"]

            # 转换为字符串格式
            logs = []
            for entry in log_entries:
                log_line = f"[{entry['timestamp']}] {entry['progress']} - {entry['log_type']}: {entry['content']}"
                logs.append(log_line)

            return logs

        except Exception as e:
            logging.error(f"获取任务日志失败: {e}")
            return [f"获取日志失败: {e}"]
    def get_runnable_tasks(self) -> List[Task]:
        """获取可运行的任务"""
        runnable = []
        for task in self.tasks.values():
            if task.status != "completed" and self.can_task_run(task.id):
                runnable.append(task)
        return runnable

    def reset_all_tasks(self) -> bool:
        """重置所有任务状态"""
        try:
            for task in self.tasks.values():
                task.status = "pending"
                task.result = ""
                task.updated_at = datetime.now().isoformat()

            # 重置session_id
            self.meta["session_id"] = None

            # 保存更改
            self.save_tasks()

            logging.info(f"项目 {self.project_name} 的所有任务已重置")
            return True

        except Exception as e:
            logging.error(f"重置任务失败: {e}")
            return False
    
    def stop_execution(self):
        """停止任务执行"""
        self.stop_event.set()
        self.is_running = False

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "execution_stopped", "任务执行已停止",
                level="warning"
            )

    def _summarize_completed_tasks(self) -> str:
        """总结已完成的任务 - 处理大数据量TODO"""
        completed_tasks = [task for task in self.tasks.values() if task.status in ["completed"]]

        if not completed_tasks:
            return ""

        # 如果任务数据超过80KB，需要分批汇总
        task_data = json.dumps([task.to_dict() for task in completed_tasks], ensure_ascii=False)

        if len(task_data.encode('utf-8')) > 80 * 1024:  # 80KB
            # 分批处理
            batch_size = 10
            summaries = []

            for i in range(0, len(completed_tasks), batch_size):
                batch = completed_tasks[i:i + batch_size]
                batch_summary = self._summarize_task_batch(batch)
                if batch_summary:
                    summaries.append(batch_summary)

            # 汇总所有批次的总结
            if summaries:
                return self._combine_summaries(summaries)
        else:
            # 直接总结所有任务
            return self._summarize_task_batch(completed_tasks)

        return ""

    def _summarize_task_batch(self, tasks: List[Task], progress_callback = None) -> str:
        """总结一批任务"""
        try:
            claude_agent = ClaudeAgent("总结已经完成的任务", self.meta.get("provider"))
            task_data = [task.to_dict() for task in tasks]

            user_req = f"""请对以下已经完成的任务进行总结，用于指导下一个任务的执行。
            # 已完成任务
            {json.dumps(task_data, ensure_ascii=False, indent=2)}

            请提供简洁的总结，包括：
            1. 主要完成的功能
            2. 遇到的问题和解决方案
            3. 对后续任务的建议
            """

             # 使用单独会话
            result = asyncio.run(claude_agent.run_agent(self.work_dir, user_req, None, "", progress_callback))
            
            if result.get("success"):
                summary = result["message"]
                if self.meta:
                    self.meta["summary"] = summary
                    self.save_tasks()
                return summary

        except Exception as e:
            logging.error(f"任务总结出错: {e}")
            result["message"] = f"任务总结出错: {e}"
        
        return result.get("message")

    def _combine_summaries(self, summaries: List[str]) -> str:
        """合并多个总结"""
        return "\n\n".join(summaries)

    def auto_run_tasks(self, parallel_mode: bool = False, progress_callback: Callable = None) -> Dict[str, Any]:
        """自动运行任务 - 完成所有TODO任务"""
        if self.is_running:
            return {"success": False, "message": "任务正在运行中"}

        self.is_running = True
        self.stop_event.clear()

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "execution_started",
                f"开始执行任务 ({'并行' if parallel_mode else '顺序'}模式)",
                level="info"
            )

        try:
            if parallel_mode:
                return self._run_tasks_parallel(progress_callback)
            else:
                return self._run_tasks_sequential(progress_callback)
        finally:
            self.is_running = False

    def _run_tasks_sequential(self, progress_callback: Callable = None) -> Dict[str, Any]:
        """顺序执行任务"""
        executed_count = 0

        while True and executed_count < len(self.tasks):
            if self.stop_event.is_set():
                break

            # 获取可运行的任务
            runnable_tasks = self.get_runnable_tasks()
            if not runnable_tasks:
                break

            # 按优先级排序
            runnable_tasks.sort(key=lambda t: {"high": 3, "medium": 2, "low": 1}.get(t.priority, 2), reverse=True)

            # 执行第一个任务
            task = runnable_tasks[0]
            success = self.run_single_task(task.id, progress_callback)
            executed_count += 1

            if not success and not self.stop_event.is_set():
                # 任务失败，记录日志但继续执行其他任务
                if self.log_manager:
                    self.log_manager.log_event(
                        self.project_name, "task_failed", f"任务执行失败: {task.title}",
                        task_id=task.id, level="error"
                    )

        return {
            "success": True,
            "message": f"顺序执行完成，共执行{executed_count}个任务",
            "executed_count": executed_count
        }

    def _run_tasks_parallel(self, progress_callback: Callable = None) -> Dict[str, Any]:
        """并行执行任务"""
        executed_count = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            while True:
                if self.stop_event.is_set():
                    break

                # 获取可运行的任务
                runnable_tasks = self.get_runnable_tasks()
                if not runnable_tasks:
                    break

                session_id = None
                # 并行任务使用新会话，总结已完成的任务
                task_summary = self._summarize_completed_tasks()

                # 提交并行任务
                future_to_task = {}
                for task in runnable_tasks[:self.max_workers]:
                    if self.stop_event.is_set():
                        break

                    # 为并发任务使用新的会话ID
                    session_id = None
                    future = executor.submit(self._run_single_task, session_id, task, task_summary, progress_callback)
                    future_to_task[future] = task

                # 等待任务完成
                for future in as_completed(future_to_task):
                    if self.stop_event.is_set():
                        break

                    task = future_to_task[future]
                    try:
                        success = future.result()
                        executed_count += 1

                        if not success:
                            if self.log_manager:
                                self.log_manager.log_event(
                                    self.project_name, "task_failed", f"并行任务执行失败: {task.title}",
                                    task_id=task.id, level="error"
                                )
                    except Exception as e:
                        if self.log_manager:
                            self.log_manager.log_event(
                                self.project_name, "task_error", f"并行任务异常: {e}",
                                task_id=task.id, level="error"
                            )

        return {
            "success": True,
            "message": f"并行执行完成，共执行{executed_count}个任务",
            "executed_count": executed_count
        }
    def restart_task(self, task_id: str) -> bool:
        """重新开始任务（使用新的会话ID）"""
        if task_id not in self.tasks:
            return False

        task = self.tasks[task_id]

        # 总结之前的结果
        previous_summary = ""
        if task.result:
            previous_summary = f"之前的执行结果: {task.result}"
        if task.error_message:
            previous_summary += f"\n之前的错误信息: {task.error_message}"

        # 重置任务状态
        task.status = "pending"
        task.result = previous_summary
        task.error_message = ""
        # 不生成新的session_id，保持原有的session_id
        task.updated_at = datetime.now().isoformat()

        if self.log_manager:
            self.log_manager.log_event(
                self.project_name, "task_restarted", f"重新开始任务: {task.title}",
                task_id=task_id, level="info"
            )

        return True

    def quick_task(self, description: str, progress_callback: Callable = None) -> Dict[str, Any]:
        """快速任务：一句话描述需求，系统自动生成任务并启动运行"""
        try:
            # 生成任务
            task_id = self.add_task(
                title=f"快速任务: {description[:50]}...",
                description=description,
                priority="high"
            )

            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "quick_task_created", f"创建快速任务: {description}",
                    task_id=task_id, level="info"
                )

            # 立即执行
            task = self.get_task(task_id)
            success = self._execute_single_task(task, progress_callback)

            return {
                "success": success,
                "task_id": task_id,
                "message": "快速任务执行完成" if success else "快速任务执行失败"
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"快速任务创建失败: {str(e)}"
            }

    def get_task_statistics(self) -> Dict[str, Any]:
        """获取任务统计信息"""
        total = len(self.tasks)
        pending = len([t for t in self.tasks.values() if t.status == "pending"])
        running = len([t for t in self.tasks.values() if t.status == "running"])
        completed = len([t for t in self.tasks.values() if t.status == "completed"])
        failed = len([t for t in self.tasks.values() if t.status == "failed"])

        return {
            "total": total,
            "pending": pending,
            "running": running,
            "completed": completed,
            "failed": failed,
            "completion_rate": (completed / total * 100) if total > 0 else 0
        }

    def export_tasks(self) -> Dict[str, Any]:
        """导出任务数据"""
        return {
            "req_id": self.project_name,
            "tasks": [task.to_dict() for task in self.tasks.values()],
            "statistics": self.get_task_statistics(),
            "exported_at": datetime.now().isoformat()
        }

    def import_tasks(self, data: Dict[str, Any]) -> bool:
        """导入任务数据"""
        try:
            if "tasks" not in data:
                return False

            for task_data in data["tasks"]:
                task = Task.from_dict(task_data)
                self.tasks[task.id] = task

            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "tasks_imported", f"导入{len(data['tasks'])}个任务",
                    level="info"
                )

            return True

        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(
                    self.project_name, "import_error", f"导入任务失败: {e}",
                    level="error"
                )
            return False

    def get_task_logs(self, limit: int = 50):
        """获取任务日志"""
        if not self.log_manager:
            return []
        
        try:
            # 从日志管理器获取日志，使用req_id作为过滤条件
            logs = self.log_manager.get_task_logs(limit=limit)
            # 过滤与当前需求相关的日志
            filtered_logs = [log for log in logs if log.get("req_id") == self.project_name or log.get("task_id") == self.project_name]
            return filtered_logs[:limit]
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(self.project_name, "log_fetch_error", f"获取日志失败: {e}", level="error")
            return []


    def _get_or_create_session_id(self):
        """从任务文件获取session_id，如果不存在则返回None"""
        try:
            if os.path.exists(self.task_file_name):
                with open(self.task_file_name, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    meta = data.get("meta", {})
                    session_id = meta.get("session_id")
                    if session_id and session_id != "None":
                        return session_id
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_event(self.project_name, "session_load_error", f"加载session_id失败: {e}", level="warning")

        # 如果无法获取，返回None而不是生成新的
        return None

