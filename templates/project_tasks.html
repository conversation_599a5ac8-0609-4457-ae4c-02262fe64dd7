{% extends "base.html" %}

{% block title %}{{ project.name }} - 任务列表 - AI任务管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <a href="{{ url_for('projects') }}" class="text-decoration-none text-muted">
            <i class="fas fa-arrow-left"></i>
        </a>
        {{ project.name }} - 任务列表
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if not project.tasks_generated %}
                <button type="button" class="btn btn-primary" onclick="generateTasks()">
                    <i class="fas fa-cogs"></i> 生成任务
                </button>
            {% else %}
                <button type="button" class="btn btn-success" onclick="runTasks()">
                    <i class="fas fa-play"></i> 运行任务
                </button>
                <button type="button" class="btn btn-warning" onclick="regenerateTasks()">
                    <i class="fas fa-redo"></i> 重新生成
                </button>
            {% endif %}
            <a href="/project/{{ project.project_id }}/files" class="btn btn-outline-info">
                <i class="fas fa-folder-open"></i> 文件预览
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshTasks()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="showAddTaskModal()">
                <i class="fas fa-plus"></i> 添加任务
            </button>
        </div>
    </div>
</div>

<!-- 项目信息卡片 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 项目信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>项目名称:</strong> {{ project.name }}</p>
                        <p><strong>项目描述:</strong> {{ project.description or '暂无描述' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建时间:</strong> {{ project.created_at[:19].replace('T', ' ') }}</p>
                        <p><strong>更新时间:</strong> {{ project.updated_at[:19].replace('T', ' ') }}</p>
                    </div>
                </div>
                {% if project.requirement %}
                <div class="mt-3">
                    <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                    <div class="border rounded p-3 bg-light">
                        <div id="requirement-content">{{ project.requirement }}</div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 任务统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-0 text-info">{{ tasks|length }}</div>
                        <small class="text-muted">总任务</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-success">{{ tasks|selectattr('status', 'equalto', 'completed')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">已完成</small>
                    </div>
                </div>
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <div class="h4 mb-0 text-warning">{{ tasks|selectattr('status', 'equalto', 'in_progress')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">进行中</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-danger">{{ tasks|selectattr('status', 'equalto', 'failed')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
{% if tasks %}
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-tasks"></i> 任务列表</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 5%">#</th>
                            <th style="width: 25%">任务标题</th>
                            <th style="width: 35%">任务描述</th>
                            <th style="width: 10%">状态</th>
                            <th style="width: 10%">优先级</th>
                            <th style="width: 15%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                <div class="fw-bold">{{ task.get('title', task.get('name', 'N/A')) }}</div>
                                {% if task.get('dependencies') %}
                                    <small class="text-muted">
                                        <i class="fas fa-link"></i> 依赖: {{ task.dependencies|join(', ') }}
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ (task.get('description', '')[:100]) }}{% if task.get('description', '')|length > 100 %}...{% endif %}</small>
                            </td>
                            <td>
                                {% set status = task.get('status', 'unknown') %}
                                {% if status == 'pending' %}
                                    <span class="badge bg-secondary">待处理</span>
                                {% elif status == 'in_progress' %}
                                    <span class="badge bg-primary">进行中</span>
                                {% elif status == 'completed' %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif status == 'failed' %}
                                    <span class="badge bg-danger">失败</span>
                                {% elif status == 'cancelled' %}
                                    <span class="badge bg-warning">已取消</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set priority = task.get('priority', 'medium') %}
                                {% if priority == 'high' %}
                                    <span class="badge bg-danger">高</span>
                                {% elif priority == 'medium' %}
                                    <span class="badge bg-warning">中</span>
                                {% elif priority == 'low' %}
                                    <span class="badge bg-secondary">低</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ priority }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    {% if task.get('status', 'pending') == 'pending' %}
                                        <button type="button" class="btn btn-outline-primary"
                                                onclick="editTask({{ task.get('id', '') }})" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="deleteTask({{ task.get('id', '') }})" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-info"
                                            onclick="viewTaskDetail({{ task.get('id', '') }})" title="详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="runSingleTask({{ task.get('id', '') }})" title="运行">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning"
                                            onclick="viewTaskLogs({{ task.get('id', '') }})" title="日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-tasks fa-5x text-muted mb-4"></i>
        <h3 class="text-muted">还没有任务</h3>
        {% if not project.tasks_generated %}
            <p class="text-muted mb-4">为项目生成任务来开始工作</p>
            <button class="btn btn-primary btn-lg" onclick="generateTasks()">
                <i class="fas fa-cogs"></i> 生成任务
            </button>
        {% else %}
            <p class="text-muted mb-4">任务加载失败或任务文件不存在</p>
            <button class="btn btn-warning btn-lg" onclick="regenerateTasks()">
                <i class="fas fa-redo"></i> 重新生成任务
            </button>
        {% endif %}
    </div>
{% endif %}
{% endblock %}

{% block modals %}
<!-- 添加任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="mb-3">
                        <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="taskTitle" required
                               placeholder="请输入任务标题">
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" rows="4"
                                  placeholder="请输入任务描述"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskPriority" class="form-label">优先级</label>
                        <select class="form-control" id="taskPriority">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                        <input type="text" class="form-control" id="taskDependencies"
                               placeholder="输入依赖的任务ID，多个用逗号分隔">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            如果此任务依赖其他任务，请输入依赖任务的ID
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="executeImmediately">
                            <label class="form-check-label" for="executeImmediately">
                                立即执行此任务
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddTask()">添加任务</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 渲染Markdown内容
    $(document).ready(function() {
        const requirementContent = $('#requirement-content');
        if (requirementContent.length && requirementContent.text().trim()) {
            requirementContent.html(marked.parse(requirementContent.text()));
        }
    });
    
    function refreshTasks() {
        location.reload();
    }

    function showAddTaskModal() {
        // 清空表单
        $('#addTaskForm')[0].reset();
        $('#addTaskModal').modal('show');
    }

    function submitAddTask() {
        const title = $('#taskTitle').val().trim();
        const description = $('#taskDescription').val().trim();
        const priority = $('#taskPriority').val();
        const dependencies = $('#taskDependencies').val().trim();
        const executeImmediately = $('#executeImmediately').is(':checked');

        if (!title) {
            showAlert('请输入任务标题', 'warning');
            return;
        }

        // 处理依赖任务ID
        const dependencyList = dependencies ? dependencies.split(',').map(id => id.trim()).filter(id => id) : [];

        // 显示加载状态
        $('#addTaskModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 添加中...');

        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                title: title,
                description: description,
                priority: priority,
                dependencies: dependencyList,
                execute_immediately: executeImmediately
            }),
            success: function(response) {
                if (response.success) {
                    let message = '任务添加成功！';
                    if (executeImmediately && response.execution_result) {
                        if (response.execution_result.success) {
                            message += ' 任务已开始执行。';
                        } else {
                            message += ` 但执行失败: ${response.execution_result.message}`;
                        }
                    }
                    showAlert(message, 'success');
                    $('#addTaskModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('任务添加失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务添加失败: ' + (response.error || '未知错误'), 'danger');
            },
            complete: function() {
                $('#addTaskModal .btn-primary').prop('disabled', false).html('添加任务');
            }
        });
    }
    
    function generateTasks() {
        if (confirm('确定要生成任务吗？')) {
            // 调用生成任务API
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/generate_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务生成成功！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务生成失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
    
    function regenerateTasks() {
        if (confirm('确定要重新生成任务吗？这将覆盖现有任务。')) {
            generateTasks();
        }
    }
    
    function runTasks() {
        if (confirm('确定要运行所有任务吗？')) {
            // 调用运行任务API
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/run_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务开始运行！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
    
    function editTask(taskId) {
        // 获取任务信息
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'GET',
            success: function(response) {
                const task = response.tasks.find(t => t.id === taskId);
                if (task) {
                    // 显示编辑对话框
                    const newTitle = prompt('任务标题:', task.title);
                    if (newTitle === null) return;

                    const newDescription = prompt('任务描述:', task.description);
                    if (newDescription === null) return;

                    const newPriority = prompt('优先级 (high/medium/low):', task.priority);
                    if (newPriority === null) return;

                    // 更新任务
                    $.ajax({
                        url: `/api/projects/{{ project.project_id }}/tasks/${taskId}`,
                        method: 'PUT',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            title: newTitle,
                            description: newDescription,
                            priority: newPriority
                        }),
                        success: function(response) {
                            if (response.success) {
                                showAlert('任务更新成功！', 'success');
                                setTimeout(() => location.reload(), 1000);
                            } else {
                                showAlert('任务更新失败: ' + response.error, 'danger');
                            }
                        },
                        error: function(xhr) {
                            const response = xhr.responseJSON || {};
                            showAlert('任务更新失败: ' + (response.error || '未知错误'), 'danger');
                        }
                    });
                } else {
                    showAlert('任务不存在', 'danger');
                }
            },
            error: function() {
                showAlert('获取任务信息失败', 'danger');
            }
        });
    }

    function deleteTask(taskId) {
        if (confirm('确定要删除这个任务吗？此操作不可撤销。')) {
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/${taskId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务删除成功！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务删除失败: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务删除失败: ' + (response.error || '未知错误'), 'danger');
                }
            });
        }
    }

    function runSingleTask(taskId) {
        if (confirm('确定要运行这个任务吗？')) {
            // 显示加载状态
            const button = $(`button[onclick="runSingleTask('${taskId}')"]`);
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/run`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务运行成功！', 'success');
                        if (response.logs && response.logs.length > 0) {
                            // 显示运行日志
                            const logHtml = response.logs.join('<br>');
                            showAlert(`任务运行完成！<br><small>${logHtml}</small>`, 'success');
                        }
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.error || '未知错误'), 'danger');
                },
                complete: function() {
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }
    }

    function viewTaskLogs(taskId) {
        // 获取任务日志
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/logs`,
            method: 'GET',
            success: function(response) {
                if (response.logs) {
                    const logHtml = `
                        <div class="modal fade" id="taskLogsModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">任务运行日志</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="bg-dark text-light p-3 rounded" style="font-family: monospace; max-height: 400px; overflow-y: auto;">
                                            ${response.logs.length > 0 ? response.logs.join('<br>') : '暂无日志记录'}
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        <button type="button" class="btn btn-primary" onclick="refreshTaskLogs('${taskId}')">刷新</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除已存在的模态框
                    $('#taskLogsModal').remove();

                    // 添加新的模态框并显示
                    $('body').append(logHtml);
                    $('#taskLogsModal').modal('show');

                    // 模态框关闭后移除
                    $('#taskLogsModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                } else {
                    showAlert('获取日志失败', 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('获取日志失败: ' + (response.error || '未知错误'), 'danger');
            }
        });
    }

    function refreshTaskLogs(taskId) {
        // 刷新日志内容
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/logs`,
            method: 'GET',
            success: function(response) {
                if (response.logs) {
                    const logContent = response.logs.length > 0 ? response.logs.join('<br>') : '暂无日志记录';
                    $('#taskLogsModal .modal-body .bg-dark').html(logContent);
                }
            },
            error: function() {
                showAlert('刷新日志失败', 'danger');
            }
        });
    }

    function viewTaskDetail(taskId) {
        // 获取任务详情
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'GET',
            success: function(response) {
                const task = response.tasks.find(t => t.id === taskId);
                if (task) {
                    const detailHtml = `
                        <div class="modal fade" id="taskDetailModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">任务详情</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <table class="table">
                                            <tr><td><strong>任务ID:</strong></td><td>${task.id}</td></tr>
                                            <tr><td><strong>标题:</strong></td><td>${task.title}</td></tr>
                                            <tr><td><strong>描述:</strong></td><td>${task.description}</td></tr>
                                            <tr><td><strong>状态:</strong></td><td>${task.status}</td></tr>
                                            <tr><td><strong>优先级:</strong></td><td>${task.priority}</td></tr>
                                            <tr><td><strong>依赖:</strong></td><td>${task.dependencies ? task.dependencies.join(', ') : '无'}</td></tr>
                                            <tr><td><strong>创建时间:</strong></td><td>${task.created_at}</td></tr>
                                            <tr><td><strong>更新时间:</strong></td><td>${task.updated_at}</td></tr>
                                        </table>
                                        ${task.result ? '<h6>执行结果:</h6><pre>' + task.result + '</pre>' : ''}
                                        ${task.error_message ? '<h6>错误信息:</h6><pre class="text-danger">' + task.error_message + '</pre>' : ''}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除已存在的模态框
                    $('#taskDetailModal').remove();

                    // 添加新的模态框并显示
                    $('body').append(detailHtml);
                    $('#taskDetailModal').modal('show');

                    // 模态框关闭后移除
                    $('#taskDetailModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                } else {
                    showAlert('任务不存在', 'danger');
                }
            },
            error: function() {
                showAlert('获取任务信息失败', 'danger');
            }
        });
    }
</script>
{% endblock %}
