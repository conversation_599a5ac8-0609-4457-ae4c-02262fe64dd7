{% extends "base.html" %}

{% block title %}{{ project.name }} - 项目详情{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item active">{{ project.name }}</li>
            </ol>
        </nav>
        <h1 class="h2">{{ project.name }}</h1>
        {% if project.description %}
            <p class="text-muted">{{ project.description }}</p>
        {% endif %}
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="createRequirement()">
                <i class="fas fa-plus"></i> 新建需求
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <a href="{{ url_for('file_browser', project_id=project.project_id) }}" class="btn btn-outline-info">
                <i class="fas fa-folder-open"></i> 文件浏览
            </a>
        </div>
    </div>
</div>

<!-- 项目统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ summary.total_tasks }}</h5>
                <p class="card-text">总任务数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ summary.completed_tasks }}</h5>
                <p class="card-text">已完成任务</p>
            </div>
        </div>
    </div>
</div>

<!-- 需求列表 -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">项目需求</h5>
    </div>
    <div class="card-body">
        {% if requirements %}
            <div class="row" id="requirements-container">
                {% for req in requirements %}
                <div class="col-lg-6 mb-3">
                    <div class="card task-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">{{ req.title }}</h6>
                                <small class="text-muted">{{ req.req_id[:8] }}</small>
                            </div>
                            <div>
                                {{ getStatusBadge(req.status)|safe }}
                                {{ getPriorityBadge(req.priority)|safe }}
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">{{ req.description[:100] }}{% if req.description|length > 100 %}...{% endif %}</p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar"></i> {{ req.created_at[:10] }}
                                </small>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('requirement_detail', req_id=req.req_id) }}" 
                                       class="btn btn-outline-primary">
                                        <i class="fas fa-eye"></i> 查看
                                    </a>
                                    <button class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split" 
                                            type="button" data-bs-toggle="dropdown">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="editRequirement('{{ req.req_id }}')">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                        </li>
                                        {% if not req.tasks_generated %}
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="generateTasks('{{ req.req_id }}')">
                                                <i class="fas fa-cogs"></i> 生成任务
                                            </a>
                                        </li>
                                        {% endif %}
                                        {% if req.tasks_generated %}
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="runTasks('{{ req.req_id }}')">
                                                <i class="fas fa-play"></i> 运行任务
                                            </a>
                                        </li>
                                        {% endif %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="deleteRequirement('{{ req.req_id }}')">
                                                <i class="fas fa-trash"></i> 删除
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-list fa-3x text-muted mb-3"></i>
                <p class="text-muted">还没有需求，<a href="#" onclick="createRequirement()">创建第一个需求</a></p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 新建需求模态框 -->
<div class="modal fade" id="createRequirementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createRequirementForm">
                    <div class="mb-3">
                        <label for="reqTitle" class="form-label">需求标题</label>
                        <input type="text" class="form-control" id="reqTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="reqDescription" class="form-label">需求描述</label>
                        <textarea class="form-control" id="reqDescription" rows="4" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="reqPriority" class="form-label">优先级</label>
                        <select class="form-select" id="reqPriority">
                            <option value="high">高</option>
                            <option value="medium" selected>中</option>
                            <option value="low">低</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRequirement()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑需求模态框 -->
<div class="modal fade" id="editRequirementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editRequirementForm">
                    <input type="hidden" id="editReqId">
                    <div class="mb-3">
                        <label for="editReqTitle" class="form-label">需求标题</label>
                        <input type="text" class="form-control" id="editReqTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="editReqDescription" class="form-label">需求描述</label>
                        <textarea class="form-control" id="editReqDescription" rows="4" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="editReqPriority" class="form-label">优先级</label>
                            <select class="form-select" id="editReqPriority">
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="editReqStatus" class="form-label">状态</label>
                            <select class="form-select" id="editReqStatus">
                                <option value="pending">待处理</option>
                                <option value="in_progress">进行中</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateRequirement()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 生成任务模态框 -->
<div class="modal fade" id="generateTasksModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>为这个需求生成AI任务？</p>
                <div class="mb-3">
                    <label for="numTasks" class="form-label">任务数量（可选）</label>
                    <input type="number" class="form-control" id="numTasks" min="1" max="20" 
                           placeholder="留空让AI自动决定">
                </div>
                <input type="hidden" id="generateTasksReqId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGenerateTasks()">生成任务</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function createRequirement() {
        $('#createRequirementModal').modal('show');
    }

    function saveRequirement() {
        const title = $('#reqTitle').val().trim();
        const description = $('#reqDescription').val().trim();
        const priority = $('#reqPriority').val();

        if (!title || !description) {
            showAlert('请填写完整的需求信息', 'warning');
            return;
        }

        $.ajax({
            url: '/api/requirements',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                project_id: '{{ project.project_id }}',
                title: title,
                description: description,
                priority: priority
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('需求创建成功！', 'success');
                    $('#createRequirementModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('需求创建失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('需求创建失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function editRequirement(reqId) {
        $.ajax({
            url: `/api/requirements/${reqId}`,
            method: 'GET',
            success: function(req) {
                $('#editReqId').val(req.req_id);
                $('#editReqTitle').val(req.title);
                $('#editReqDescription').val(req.description);
                $('#editReqPriority').val(req.priority);
                $('#editReqStatus').val(req.status);
                $('#editRequirementModal').modal('show');
            },
            error: function() {
                showAlert('获取需求信息失败', 'danger');
            }
        });
    }

    function updateRequirement() {
        const reqId = $('#editReqId').val();
        const title = $('#editReqTitle').val().trim();
        const description = $('#editReqDescription').val().trim();
        const priority = $('#editReqPriority').val();
        const status = $('#editReqStatus').val();

        if (!title || !description) {
            showAlert('请填写完整的需求信息', 'warning');
            return;
        }

        $.ajax({
            url: `/api/requirements/${reqId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                title: title,
                description: description,
                priority: priority,
                status: status
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('需求更新成功！', 'success');
                    $('#editRequirementModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('需求更新失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('需求更新失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function generateTasks(reqId) {
        $('#generateTasksReqId').val(reqId);
        $('#generateTasksModal').modal('show');
    }

    function confirmGenerateTasks() {
        const reqId = $('#generateTasksReqId').val();
        const numTasks = $('#numTasks').val() || null;

        showAlert('正在生成任务，请稍候...', 'info');
        $('#generateTasksModal').modal('hide');

        $.ajax({
            url: `/api/requirements/${reqId}/generate_tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                num_tasks: numTasks ? parseInt(numTasks) : null
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('任务生成成功！', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('任务生成失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function runTasks(reqId) {
        if (confirm('确定要运行这个需求的所有任务吗？')) {
            showAlert('任务开始运行，请稍候...', 'info');

            $.ajax({
                url: `/api/requirements/${reqId}/run_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    parallel_mode: true
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已开始运行！', 'success');
                        // 可以跳转到需求详情页面查看进度
                        setTimeout(() => {
                            window.location.href = `/requirement/${reqId}`;
                        }, 1000);
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function deleteRequirement(reqId) {
        if (confirm('确定要删除这个需求吗？此操作将同时删除相关的任务和日志。')) {
            $.ajax({
                url: `/api/requirements/${reqId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showAlert('需求删除成功！', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showAlert('需求删除失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('需求删除失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
</script>
{% endblock %}
