{"projects": [{"project_id": "fd23027e-aaf1-4d2b-ac63-ce93c0d5a354", "name": "俄罗斯方块", "work_dir": "/mnt/d/agent/auto-claude-tasks/demo", "description": "", "requirement": "使用html生成一个俄罗斯方块游戏", "created_at": "2025-09-25T20:29:17.275019", "updated_at": "2025-09-27T20:23:39.705787", "tasks_generated": true, "task_manager_session": "3ce2e906-19e9-4f36-8d9d-f5baa8bd80ba"}, {"project_id": "a0e53b6d-6f24-499d-9d03-d53957248a23", "name": "API测试项目(已更新)", "work_dir": "/tmp/api_test_project", "description": "通过API更新的测试项目", "requirement": "# API测试项目需求\n\n## 功能要求\n1. 用户管理系统\n2. 数据分析功能\n3. 报表生成\n\n## 技术要求\n- Python Flask\n- SQLite数据库\n- Bootstrap前端\n\n\n## 更新内容\n- 添加了新的功能要求", "created_at": "2025-09-25T22:08:43.807352", "updated_at": "2025-09-25T22:08:43.814677", "tasks_generated": false, "task_manager_session": null}, {"project_id": "1758977931726", "name": "csdk项目C语言重构", "work_dir": "/mnt/d/aicode/csdkc/", "description": "csdk项目C语言重构", "requirement": "1. 使用纯C语言重构当前项目的src目录下的所有cpp代码，存储到src-c目录下\n 2. 重构完成后，修改CMakeLists.txt文件，使用纯C语言的源文件目录编译，在build目录使用cmake -DWITH_TESTS=ON ..编译确保编译成功\n 3. 并运行自动化测试：cd build/tests && ./auto_CCSP 1，分析测试失败的用例，并解决", "created_at": "2025-09-27T20:58:51.730570", "updated_at": "2025-09-27T23:40:41.206368", "tasks_generated": true, "task_manager_session": "5be9f4d9-d801-4c98-8635-bb9b88703ddc"}], "meta": {"total_projects": 3, "last_updated": "2025-09-27T23:40:41.206868"}}